# -*- coding: utf-8 -*-
"""
模型定义文件 - 包含VAD+SR多任务模型的完整定义
基于TODO.md中的VAD_SR_Model架构
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import math

class SincConv_fast(nn.Module):
    """快速SincConv实现 - 用于音频前端特征提取"""
    
    @staticmethod
    def to_mel(hz):
        return 2595 * np.log10(1 + hz / 700)
    
    @staticmethod
    def to_hz(mel):
        return 700 * (10**(mel / 2595) - 1)
    
    def __init__(self, in_channels, out_channels, kernel_size, sample_rate=8000, 
                 stride=1, min_low_hz=50, min_band_hz=50):
        super().__init__()
        
        self.in_channels = in_channels
        self.out_channels = out_channels
        self.sample_rate = sample_rate
        self.stride = stride
        self.min_low_hz = min_low_hz
        self.min_band_hz = min_band_hz
        
        # 确保kernel_size为奇数
        if kernel_size % 2 == 0:
            kernel_size += 1
        self.kernel_size = kernel_size
            
        # 创建时间索引 - 确保长度与kernel_size一致
        # 对于kernel_size=129，需要129个点，从-64到64
        half_size = self.kernel_size // 2
        self.n_ = torch.arange(-half_size, half_size + 1, dtype=torch.float).view(1, -1)
        
        # 验证长度
        assert self.n_.size(1) == self.kernel_size, f"n_ length {self.n_.size(1)} != kernel_size {self.kernel_size}"
        
        # 初始化频率参数
        low_hz = self.min_low_hz
        high_hz = self.sample_rate / 2 - (self.min_low_hz + self.min_band_hz)
        
        mel = torch.linspace(self.to_mel(low_hz), self.to_mel(high_hz), self.out_channels + 1)
        hz = self.to_hz(mel)
        
        self.low_hz_ = nn.Parameter(hz[:-1].view(-1, 1))
        self.band_hz_ = nn.Parameter((hz[1:] - hz[:-1]).view(-1, 1))
        
        # 创建窗口函数 - 使用确定的kernel_size
        self.window_ = torch.hamming_window(self.kernel_size)

    def forward(self, x):
        # 确保所有张量在相同设备上
        self.n_ = self.n_.to(x.device)
        self.window_ = self.window_.to(x.device)
        
        f_low = torch.abs(self.low_hz_) + self.min_low_hz
        f_high = torch.clamp(f_low + self.band_hz_, self.min_low_hz, self.sample_rate / 2)
        
        band = (f_high - f_low)
        f_c = (f_low + f_high) / 2
        
        n_pi = self.n_ * (2 * math.pi)
        low_pass1 = 2 * f_c * torch.sinc(n_pi * f_c / self.sample_rate)
        low_pass2 = 2 * (f_c + band/2) * torch.sinc(n_pi * (f_c + band/2) / self.sample_rate)
        
        band_pass = (low_pass2 - low_pass1)
        band_pass = band_pass / band_pass.max(dim=1, keepdim=True)[0]
        
        # 确保window和band_pass维度匹配
        # band_pass: [out_channels, kernel_size]
        # window_: [kernel_size]
        # 将window扩展到匹配band_pass的维度
        window = self.window_.unsqueeze(0).expand(self.out_channels, -1)
        
        # 应用窗口函数
        windowed_filters = band_pass * window
        
        # 重塑为卷积滤波器格式 [out_channels, in_channels, kernel_size]
        filters = windowed_filters.view(self.out_channels, 1, self.kernel_size)
        
        return F.conv1d(x, filters, stride=self.stride, 
                       padding=self.kernel_size//2)


class SEBlock(nn.Module):
    """Squeeze-and-Excitation模块 - 优化的轻量级版本"""
    
    def __init__(self, channels, reduction=8):  # 恢复到8以增加容量
        super().__init__()
        self.pool = nn.AdaptiveAvgPool1d(1)
        # 确保至少有1个隐藏单元
        hidden_dim = max(1, channels // reduction)
        self.fc = nn.Sequential(
            nn.Linear(channels, hidden_dim, bias=False),
            nn.ReLU(inplace=True),
            nn.Linear(hidden_dim, channels, bias=False),
            nn.Sigmoid()
        )
    
    def forward(self, x):
        b, c, _ = x.size()
        y = self.pool(x).view(b, c)
        y = self.fc(y).view(b, c, 1)
        return x * y.expand_as(x)


class ResBlock(nn.Module):
    """残差块"""
    
    def __init__(self, in_channels, out_channels, stride=1):
        super().__init__()
        self.conv1 = nn.Conv1d(in_channels, out_channels, 3, stride, padding=1, bias=False)
        self.bn1 = nn.BatchNorm1d(out_channels)
        self.relu = nn.ReLU(inplace=True)
        self.conv2 = nn.Conv1d(out_channels, out_channels, 3, padding=1, bias=False)
        self.bn2 = nn.BatchNorm1d(out_channels)
        self.se = SEBlock(out_channels)
        
        if stride != 1 or in_channels != out_channels:
            self.shortcut = nn.Sequential(
                nn.Conv1d(in_channels, out_channels, 1, stride, bias=False),
                nn.BatchNorm1d(out_channels)
            )
        else:
            self.shortcut = nn.Identity()
    
    def forward(self, x):
        residual = self.shortcut(x)
        out = self.relu(self.bn1(self.conv1(x)))
        out = self.bn2(self.conv2(out))
        out = self.se(out)
        out += residual
        return self.relu(out)


class AttentiveStatPooling(nn.Module):
    """注意力统计池化 - 中等容量版本"""
    
    def __init__(self, in_dim, bottleneck_dim=64):  # 从48增加到64
        super().__init__()
        self.tdnn = nn.Sequential(
            nn.Conv1d(in_dim, bottleneck_dim, 1),
            nn.ReLU(),
            nn.BatchNorm1d(bottleneck_dim),
            nn.Conv1d(bottleneck_dim, in_dim, 1)
        )
    
    def forward(self, x, mask=None):
        alpha = torch.softmax(self.tdnn(x), dim=2)
        
        if mask is not None:
            alpha = alpha * mask
            
        mean = torch.sum(alpha * x, dim=2)
        var = torch.sum(alpha * (x**2), dim=2) - mean**2
        std = torch.sqrt(var.clamp(min=1e-5))
        
        return torch.cat([mean, std], dim=1)


class SharedBackbone(nn.Module):
    """共享主干网络 - 中等轻量级版本"""
    
    def __init__(self, in_channels=1, channels=32, sample_rate=8000):  # 修正默认值为32
        super().__init__()
        
        # 使用更大的SincConv核提升性能
        self.frontend = SincConv_fast(in_channels, channels, kernel_size=97, 
                                     sample_rate=sample_rate)
        self.bn_frontend = nn.BatchNorm1d(channels)
        
        # 增加更多层以提升模型容量
        self.layers = nn.Sequential(
            ResBlock(channels, channels, stride=2),         # 32 -> 32
            ResBlock(channels, channels * 2, stride=2),     # 32 -> 64
            ResBlock(channels * 2, channels * 2, stride=1), # 64 -> 64 (增加深度)
            ResBlock(channels * 2, channels * 2, stride=1), # 64 -> 64 (再增加一层)
        )
        
        self.output_channels = channels * 2  # 64

    def forward(self, x):
        x = self.frontend(x)
        x = F.leaky_relu(self.bn_frontend(x))
        x = self.layers(x)
        return x


class VAD_SR_Model(nn.Module):
    """VAD + Speaker Recognition 多任务模型"""
    
    def __init__(self, in_channels=1, channels=32, embedding_size=192, sample_rate=8000):
        super().__init__()
        
        # 共享主干网络
        self.backbone = SharedBackbone(in_channels, channels, sample_rate)
        backbone_out_channels = self.backbone.output_channels

        # VAD任务头 (逐帧分类)
        self.vad_head = nn.Conv1d(in_channels=backbone_out_channels, 
                                 out_channels=1, kernel_size=1)

        # SR任务头 (生成声纹)
        self.sr_pooling = AttentiveStatPooling(in_dim=backbone_out_channels)
        self.sr_embedding = nn.Sequential(
            nn.Linear(backbone_out_channels * 2, embedding_size),
            nn.BatchNorm1d(embedding_size)
        )

    def forward(self, x):
        """
        前向传播，同时输出声纹和VAD结果
        
        Args:
            x: 原始波形, shape: [batch_size, num_samples]
            
        Returns:
            speaker_embedding: [batch_size, embedding_size]
            vad_logits: [batch_size, T_frames]
        """
        if x.ndim == 2:
            x = x.unsqueeze(1)  # 保证输入是 [B, 1, N]
        
        # 通过共享主干网络提取特征
        frame_features = self.backbone(x)

        # VAD分支: 计算每一帧的语音活动概率
        vad_logits = self.vad_head(frame_features)

        # SR分支: 利用VAD结果指导声纹提取
        with torch.no_grad():
            vad_scores = torch.sigmoid(vad_logits)

        # 将语音活动分数作为注意力掩码传入池化层
        pooled_features = self.sr_pooling(frame_features, mask=vad_scores)
        
        # 生成最终的声纹嵌入
        speaker_embedding = self.sr_embedding(pooled_features)

        # 返回两个任务的结果
        return speaker_embedding, vad_logits.squeeze(1)


if __name__ == '__main__':
    # 测试模型
    model = VAD_SR_Model(channels=32, embedding_size=192)
    dummy_input = torch.randn(4, 32000)  # 4秒音频
    
    speaker_embedding, vad_logits = model(dummy_input)
    
    print(f"Speaker embedding shape: {speaker_embedding.shape}")
    print(f"VAD logits shape: {vad_logits.shape}")
    
    num_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"Model parameters: {num_params / 1e6:.2f} M")
