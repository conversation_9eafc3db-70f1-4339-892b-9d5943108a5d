# -*- coding: utf-8 -*-
"""
推理脚本 - VAD + Speaker Recognition 多任务模型推理
"""

import os
import json
import argparse
import logging
from datetime import datetime
from typing import Dict, List, Tuple

import torch
import torch.nn.functional as F
import torchaudio
import numpy as np
import matplotlib.pyplot as plt

from config import Config
from model import VAD_SR_Model
from loss import AAMSoftmax


class AudioInference:
    """音频推理类"""
    
    def __init__(self, model_path: str, config: Config = None):
        self.config = config or Config()
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 加载模型
        self.model = self._load_model(model_path)
        self.model.eval()
        
        # 设置日志
        self.logger = self._setup_logger()
        
        self.logger.info(f"推理器初始化完成，使用设备: {self.device}")
    
    def _setup_logger(self) -> logging.Logger:
        logger = logging.getLogger('AudioInference')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def _load_model(self, model_path: str) -> VAD_SR_Model:
        """加载训练好的模型"""
        if not os.path.exists(model_path):
            raise FileNotFoundError(f"模型文件不存在: {model_path}")
        
        # 创建模型实例
        model = VAD_SR_Model(
            channels=self.config.CHANNELS,
            embedding_size=self.config.EMBEDDING_SIZE,
            sample_rate=self.config.SAMPLE_RATE
        )
        
        # 加载权重
        checkpoint = torch.load(model_path, map_location=self.device)
        
        if 'model_state_dict' in checkpoint:
            model.load_state_dict(checkpoint['model_state_dict'])
            self.logger.info(f"从检查点加载模型，epoch: {checkpoint.get('epoch', 'unknown')}")
        else:
            model.load_state_dict(checkpoint)
            self.logger.info("直接加载模型权重")
        
        model.to(self.device)
        return model
    
    def _load_audio(self, audio_path: str) -> torch.Tensor:
        """加载音频文件并预处理"""
        if not os.path.exists(audio_path):
            raise FileNotFoundError(f"音频文件不存在: {audio_path}")
        
        # 加载音频
        waveform, sample_rate = torchaudio.load(audio_path)
        
        # 转换为单声道
        if waveform.shape[0] > 1:
            waveform = torch.mean(waveform, dim=0, keepdim=True)
        
        # 重采样到目标采样率
        if sample_rate != self.config.SAMPLE_RATE:
            resampler = torchaudio.transforms.Resample(
                orig_freq=sample_rate,
                new_freq=self.config.SAMPLE_RATE
            )
            waveform = resampler(waveform)
        
        return waveform
    
    def _segment_audio(self, waveform: torch.Tensor, segment_length: float = 2.0) -> List[torch.Tensor]:
        """将长音频分段处理"""
        num_samples = waveform.shape[1]
        segment_samples = int(segment_length * self.config.SAMPLE_RATE)
        
        segments = []
        start = 0
        
        while start < num_samples:
            end = min(start + segment_samples, num_samples)
            segment = waveform[:, start:end]
            
            # 如果分段太短，进行零填充
            if segment.shape[1] < segment_samples:
                padding = segment_samples - segment.shape[1]
                segment = F.pad(segment, (0, padding))
            
            segments.append(segment)
            
            if end >= num_samples:
                break
            start += segment_samples // 2  # 50% 重叠
        
        return segments
    
    def predict_single(self, audio_path: str, segment_length: float = 2.0) -> Dict:
        """对单个音频文件进行推理"""
        self.logger.info(f"开始推理音频文件: {audio_path}")
        
        # 加载音频
        waveform = self._load_audio(audio_path)
        
        # 分段处理
        segments = self._segment_audio(waveform, segment_length)
        
        all_embeddings = []
        all_vad_results = []
        
        with torch.no_grad():
            for segment in segments:
                segment = segment.to(self.device)
                
                # 模型推理
                speaker_embedding, vad_logits = self.model(segment)
                
                # 处理结果
                speaker_embedding = speaker_embedding.cpu().numpy()
                vad_probs = torch.sigmoid(vad_logits).cpu().numpy()
                
                all_embeddings.append(speaker_embedding[0])
                all_vad_results.append(vad_probs[0])
        
        # 合并结果
        final_embedding = np.mean(all_embeddings, axis=0)  # 平均所有分段的嵌入
        final_vad = np.concatenate(all_vad_results)  # 连接所有VAD结果
        
        # 说话人识别
        speaker_id, confidence = self._identify_speaker(final_embedding)
        
        # VAD统计
        vad_stats = self._compute_vad_stats(final_vad)
        
        result = {
            'audio_path': audio_path,
            'duration': waveform.shape[1] / self.config.SAMPLE_RATE,
            'speaker_recognition': {
                'speaker_id': speaker_id,
                'speaker_name': self.config.ID_TO_SPEAKER.get(speaker_id, 'unknown'),
                'confidence': float(confidence),
                'embedding': final_embedding.tolist()
            },
            'vad': {
                'frame_probs': final_vad.tolist(),
                'speech_ratio': vad_stats['speech_ratio'],
                'speech_segments': vad_stats['speech_segments'],
                'total_speech_time': vad_stats['total_speech_time']
            },
            'segments_processed': len(segments)
        }
        
        self.logger.info(f"推理完成 - 说话人: {result['speaker_recognition']['speaker_name']}, "
                        f"置信度: {confidence:.3f}, 语音比例: {vad_stats['speech_ratio']:.2%}")
        
        return result
    
    def _identify_speaker(self, embedding: np.ndarray) -> Tuple[int, float]:
        """基于嵌入向量识别说话人"""
        # 将嵌入转换为tensor并归一化
        embedding_tensor = torch.from_numpy(embedding).unsqueeze(0).to(self.device)
        embedding_tensor = F.normalize(embedding_tensor, p=2, dim=1)
        
        # 使用余弦相似度进行分类（简化版本）
        # 在实际应用中，您需要预先存储的说话人原型向量
        with torch.no_grad():
            # 这里使用随机初始化的原型向量作为示例
            # 实际应用中应该使用训练时学到的分类器权重
            prototypes = torch.randn(self.config.NUM_CLASSES, self.config.EMBEDDING_SIZE).to(self.device)
            prototypes = F.normalize(prototypes, p=2, dim=1)
            
            # 计算余弦相似度
            similarities = torch.mm(embedding_tensor, prototypes.t())
            probs = F.softmax(similarities * 10, dim=1)  # 温度缩放
            
            speaker_id = torch.argmax(probs, dim=1).item()
            confidence = torch.max(probs, dim=1)[0].item()
        
        return speaker_id, confidence
    
    def _compute_vad_stats(self, vad_probs: np.ndarray, threshold: float = 0.5) -> Dict:
        """计算VAD统计信息"""
        # 二值化
        vad_binary = (vad_probs > threshold).astype(int)
        
        # 计算语音比例
        speech_ratio = np.mean(vad_binary)
        
        # 找到语音段
        speech_segments = []
        in_speech = False
        start_frame = 0
        
        for i, is_speech in enumerate(vad_binary):
            if is_speech and not in_speech:
                start_frame = i
                in_speech = True
            elif not is_speech and in_speech:
                speech_segments.append((start_frame, i - 1))
                in_speech = False
        
        if in_speech:
            speech_segments.append((start_frame, len(vad_binary) - 1))
        
        # 计算总语音时间
        frame_duration = self.config.VAD_FRAME_SHIFT
        total_speech_time = len(vad_binary) * frame_duration * speech_ratio
        
        return {
            'speech_ratio': speech_ratio,
            'speech_segments': speech_segments,
            'total_speech_time': total_speech_time,
            'num_speech_segments': len(speech_segments)
        }
    
    def visualize_result(self, result: Dict, save_path: str = None):
        """可视化推理结果"""
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8))
        
        # VAD结果可视化
        vad_probs = np.array(result['vad']['frame_probs'])
        time_axis = np.arange(len(vad_probs)) * self.config.VAD_FRAME_SHIFT
        
        ax1.plot(time_axis, vad_probs, 'b-', linewidth=1)
        ax1.axhline(y=0.5, color='r', linestyle='--', alpha=0.7, label='Threshold')
        ax1.fill_between(time_axis, 0, vad_probs, alpha=0.3)
        ax1.set_ylabel('VAD Probability')
        ax1.set_title(f'Voice Activity Detection - {os.path.basename(result["audio_path"])}')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 说话人识别结果
        speaker_info = result['speaker_recognition']
        ax2.text(0.1, 0.8, f"Speaker: {speaker_info['speaker_name']}", 
                transform=ax2.transAxes, fontsize=14, fontweight='bold')
        ax2.text(0.1, 0.6, f"Confidence: {speaker_info['confidence']:.3f}", 
                transform=ax2.transAxes, fontsize=12)
        ax2.text(0.1, 0.4, f"Speech Ratio: {result['vad']['speech_ratio']:.2%}", 
                transform=ax2.transAxes, fontsize=12)
        ax2.text(0.1, 0.2, f"Duration: {result['duration']:.2f}s", 
                transform=ax2.transAxes, fontsize=12)
        ax2.set_xlim(0, 1)
        ax2.set_ylim(0, 1)
        ax2.axis('off')
        ax2.set_title('Speaker Recognition Result')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            self.logger.info(f"可视化结果保存到: {save_path}")
        else:
            plt.show()
        
        plt.close()


def main():
    parser = argparse.ArgumentParser(description='VAD + Speaker Recognition 推理')
    parser.add_argument('--model_path', type=str, required=True,
                       help='训练好的模型路径')
    parser.add_argument('--audio_path', type=str, required=True,
                       help='音频文件路径')
    parser.add_argument('--output_dir', type=str, default='results',
                       help='结果输出目录')
    parser.add_argument('--segment_length', type=float, default=2.0,
                       help='音频分段长度（秒）')
    parser.add_argument('--visualize', action='store_true',
                       help='是否生成可视化结果')
    
    args = parser.parse_args()
    
    # 创建推理器
    config = Config()
    inferencer = AudioInference(args.model_path, config)
    
    # 推理
    result = inferencer.predict_single(args.audio_path, args.segment_length)
    
    # 保存结果
    os.makedirs(args.output_dir, exist_ok=True)
    result_file = os.path.join(args.output_dir, 
                             f"{os.path.splitext(os.path.basename(args.audio_path))[0]}_result.json")
    with open(result_file, 'w', encoding='utf-8') as f:
        json.dump(result, f, ensure_ascii=False, indent=2)
    
    print(f"推理结果保存到: {result_file}")
    
    # 可视化
    if args.visualize:
        viz_path = os.path.join(args.output_dir, 
                              f"{os.path.splitext(os.path.basename(args.audio_path))[0]}_analysis.png")
        inferencer.visualize_result(result, viz_path)


if __name__ == '__main__':
    main()