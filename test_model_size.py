# -*- coding: utf-8 -*-
"""
测试超轻量级模型的参数数量
"""

import torch
from model import VAD_SR_Model
from config import Config

def count_parameters(model):
    """计算模型参数数量"""
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    return total_params, trainable_params

def calculate_model_size(num_params):
    """计算模型存储大小"""
    # 每个参数4字节 (float32)
    size_bytes = num_params * 4
    size_kb = size_bytes / 1024
    size_mb = size_kb / 1024
    return size_bytes, size_kb, size_mb

def test_ultra_light_model():
    """测试超轻量级模型"""
    config = Config()
    
    print("=== 超轻量级VAD+SR模型测试 ===")
    print(f"通道数: {config.CHANNELS}")
    print(f"嵌入维度: {config.EMBEDDING_SIZE}")
    print()
    
    # 创建模型
    model = VAD_SR_Model(
        channels=config.CHANNELS,
        embedding_size=config.EMBEDDING_SIZE,
        sample_rate=config.SAMPLE_RATE
    )
    
    # 计算参数数量
    total_params, trainable_params = count_parameters(model)
    size_bytes, size_kb, size_mb = calculate_model_size(total_params)
    
    print(f"总参数数量: {total_params:,}")
    print(f"可训练参数: {trainable_params:,}")
    print()
    print(f"模型大小:")
    print(f"  {size_bytes:,} 字节")
    print(f"  {size_kb:.1f} KB")
    print(f"  {size_mb:.3f} MB")
    print()
    
    # 检查是否达到100KB目标
    target_kb = 100
    if size_kb <= target_kb:
        print(f"✅ 成功！模型大小 {size_kb:.1f}KB 已达到目标 {target_kb}KB")
    else:
        print(f"❌ 未达标！模型大小 {size_kb:.1f}KB 超过目标 {target_kb}KB")
        reduction_needed = size_kb / target_kb
        print(f"   需要进一步缩减 {reduction_needed:.1f} 倍")
    
    print()
    
    # 测试前向传播
    batch_size = 4
    audio_length = int(config.SEGMENT_LENGTH * config.SAMPLE_RATE)  # 2秒音频
    dummy_input = torch.randn(batch_size, audio_length)
    
    print("测试前向传播...")
    try:
        with torch.no_grad():
            speaker_embedding, vad_logits = model(dummy_input)
        
        print(f"✅ 前向传播成功")
        print(f"   输入形状: {dummy_input.shape}")
        print(f"   说话人嵌入: {speaker_embedding.shape}")
        print(f"   VAD输出: {vad_logits.shape}")
        
    except Exception as e:
        print(f"❌ 前向传播失败: {e}")
    
    return size_kb <= target_kb

if __name__ == '__main__':
    success = test_ultra_light_model()
    print(f"\n{'='*50}")
    if success:
        print("🎉 超轻量级模型构建成功！")
    else:
        print("⚠️  模型需要进一步优化")
    print(f"{'='*50}")
