# -*- coding: utf-8 -*-
"""
配置文件 - 定义所有超参数和路径
"""

import os

class Config:
    """训练配置"""
    
    # 数据集路径
    DATASET_ROOT = "VeriHealthi_SR_Dataset_v2.0"
    
    # 模型参数 - 中等轻量级配置 (目标200KB)
    SAMPLE_RATE = 8000
    CHANNELS = 24       # 从16增加到24
    EMBEDDING_SIZE = 128 # 从96增加到128 
    NUM_CLASSES = 5  # 4个目标说话人 + 1个其他说话人
    
    # 训练参数 - 优化版本减少损失波动
    BATCH_SIZE = 16         # 减小批量大小提高稳定性
    NUM_EPOCHS = 100
    LEARNING_RATE = 0.0005   # 降低学习率减少波动
    WEIGHT_DECAY = 1e-4      # 减小正则化强度
    
    # 学习率调度参数
    LR_SCHEDULER_STEP = 20   # 每20个epoch衰减学习率
    LR_SCHEDULER_GAMMA = 0.7 # 学习率衰减因子
    
    # 早停参数
    EARLY_STOP_PATIENCE = 20  # 增加耐心值
    MIN_EPOCHS = 15          # 增加最小epoch数
    
    # 多任务损失权重 - 重新平衡
    VAD_LOSS_WEIGHT = 0.3    # 降低VAD权重，SR任务权重为0.7
    
    # 数据加载参数
    NUM_WORKERS = 0  # Windows上推荐设为0避免多进程问题
    SEGMENT_LENGTH = 2  # 音频分段长度(秒)
    
    # 目标说话人映射
    SPEAKER_MAPPING = {
        'XiaoXin': 0,
        'XiaoYuan': 1, 
        'XiaoSi': 2,
        'XiaoLai': 3,
        'others': 4  # 其他说话人
    }
    
    # 反向映射
    ID_TO_SPEAKER = {v: k for k, v in SPEAKER_MAPPING.items()}
    
    # 设备类型优先级 (开发板数据优先)
    DEVICE_PRIORITY = ['board', 'phone', 'pc', 'ipad']
    
    # 模型保存路径
    MODEL_SAVE_DIR = "checkpoints"
    LOG_DIR = "logs"
    
    # AAMSoftmax参数 - 更温和的设置减少损失波动
    AAM_MARGIN = 0.1   # 减小margin从0.2到0.1
    AAM_SCALE = 20.0   # 减小scale从30到20
    
    # VAD标注参数
    VAD_FRAME_LENGTH = 0.025  # 25ms帧长
    VAD_FRAME_SHIFT = 0.01   # 10ms帧移
    
    @classmethod
    def create_dirs(cls):
        """创建必要的目录"""
        os.makedirs(cls.MODEL_SAVE_DIR, exist_ok=True)
        os.makedirs(cls.LOG_DIR, exist_ok=True)
