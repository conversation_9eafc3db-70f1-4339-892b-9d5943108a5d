# -*- coding: utf-8 -*-
"""
测试早停功能的脚本
"""

import torch
import torch.nn as nn
from config import Config
from model import VAD_SR_Model
from train import Trainer

def simulate_trainer():
    """模拟训练器测试早停逻辑"""
    
    # 创建配置
    config = Config()
    config.EARLY_STOP_PATIENCE = 3  # 测试用，设为3个epoch
    config.MIN_EPOCHS = 2
    
    # 创建虚拟模型
    model = VAD_SR_Model(channels=32, embedding_size=128)
    
    # 创建虚拟数据加载器
    class DummyLoader:
        def __len__(self):
            return 10
        def __iter__(self):
            for i in range(10):
                yield (torch.randn(4, 16000), torch.randint(0, 5, (4,)), torch.randn(4, 100))
    
    train_loader = DummyLoader()
    val_loader = DummyLoader()
    
    # 创建训练器（但不初始化完整的训练器）
    trainer = Trainer.__new__(Trainer)
    trainer.model = model
    trainer.config = config
    trainer.device = torch.device('cpu')
    trainer.patience = config.EARLY_STOP_PATIENCE
    trainer.early_stop_counter = 0
    trainer.early_stop = False
    trainer.min_epochs = config.MIN_EPOCHS
    trainer.current_epoch = 0
    trainer.best_val_loss = float('inf')
    
    import logging
    trainer.logger = logging.getLogger(__name__)
    logging.basicConfig(level=logging.INFO)
    
    # 模拟验证损失序列
    val_losses = [2.5, 2.0, 1.8, 2.1, 2.3, 2.4, 2.2, 1.5, 1.6, 1.7]  # 第8个epoch有改善
    
    print("=== 测试早停功能 ===")
    print(f"Patience: {trainer.patience}")
    print(f"Min epochs: {trainer.min_epochs}")
    print()
    
    for epoch, val_loss in enumerate(val_losses):
        trainer.current_epoch = epoch
        
        print(f"Epoch {epoch}: Val Loss = {val_loss:.3f}")
        
        # 检查早停
        trainer.check_early_stopping(val_loss)
        
        print(f"  Early stop counter: {trainer.early_stop_counter}/{trainer.patience}")
        print(f"  Best val loss: {trainer.best_val_loss:.3f}")
        print(f"  Early stop: {trainer.early_stop}")
        print()
        
        # 如果触发早停，跳出循环
        if trainer.early_stop and epoch >= trainer.min_epochs:
            print(f"✅ 早停在第 {epoch} 个epoch触发！")
            break
    else:
        print("✅ 训练正常完成，没有触发早停")

if __name__ == '__main__':
    simulate_trainer()
