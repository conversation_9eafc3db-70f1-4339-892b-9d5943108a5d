@echo off
REM 启动稳定化训练脚本

echo === Starting Stable Training ===
echo.
echo 使用的优化配置:
echo - 降低学习率: 0.001 -> 0.0005
echo - 减小批量大小: 32 -> 16
echo - 更温和的损失权重: VAD 0.5 -> 0.3
echo - 减小AAM参数: margin 0.2->0.1, scale 30->20
echo - 添加梯度稳定化和监控
echo.

REM 激活conda环境（如果需要）
REM call conda activate your_env_name

REM 开始训练
python train.py --batch_size 16 --learning_rate 0.0005

REM 训练完成后分析结果
echo.
echo === Training Analysis ===
python monitor_training.py

pause
