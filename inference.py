# -*- coding: utf-8 -*-
"""
推理脚本 - 使用训练好的VAD+SR多任务模型进行推理
可用于单个音频文件或批量音频文件的推理
"""

import os
import argparse
import time
import json
import numpy as np
import torch
import torchaudio
import matplotlib.pyplot as plt
from tqdm import tqdm

from config import Config
from model import VAD_SR_Model


class Inference:
    """推理类，用于加载模型和执行推理"""

    def __init__(self, model_path, config=None, device=None):
        """
        初始化推理模型
        
        Args:
            model_path: 模型文件路径
            config: 配置对象，如果为None则使用默认配置
            device: 设备，如果为None则自动选择
        """
        self.config = config or Config()
        self.device = device or torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        print(f"Using device: {self.device}")
        
        # 创建模型
        self.model = VAD_SR_Model(
            channels=self.config.CHANNELS,
            embedding_size=self.config.EMBEDDING_SIZE,
            sample_rate=self.config.SAMPLE_RATE
        )
        
        # 加载模型权重
        checkpoint = torch.load(model_path, map_location=self.device)
        if 'model_state_dict' in checkpoint:
            self.model.load_state_dict(checkpoint['model_state_dict'])
        else:
            self.model.load_state_dict(checkpoint)
            
        self.model.to(self.device)
        self.model.eval()
        
        print(f"Model loaded from {model_path}")
    
    def preprocess_audio(self, audio_path):
        """
        预处理音频文件
        
        Args:
            audio_path: 音频文件路径
            
        Returns:
            waveform: 预处理后的波形数据
            sr: 原始采样率
        """
        try:
            # 加载音频
            waveform, sr = torchaudio.load(audio_path)
            
            # 转换为单声道
            if waveform.shape[0] > 1:
                waveform = torch.mean(waveform, dim=0, keepdim=True)
            
            # 重采样到目标采样率
            if sr != self.config.SAMPLE_RATE:
                resampler = torchaudio.transforms.Resample(sr, self.config.SAMPLE_RATE)
                waveform = resampler(waveform)
            
            return waveform, sr
            
        except Exception as e:
            print(f"Error processing {audio_path}: {e}")
            return None, None
    
    def segment_audio(self, waveform, segment_length=4, overlap=0.5):
        """
        将长音频分段处理
        
        Args:
            waveform: 音频波形
            segment_length: 分段长度(秒)
            overlap: 重叠比例(0-1之间)
            
        Returns:
            segments: 分段后的音频列表
            segment_times: 每段的开始时间
        """
        samples_per_segment = int(segment_length * self.config.SAMPLE_RATE)
        hop_size = int(samples_per_segment * (1 - overlap))
        
        # 获取总样本数
        total_samples = waveform.shape[1]
        
        segments = []
        segment_times = []
        
        # 分段处理
        for start in range(0, total_samples, hop_size):
            end = min(start + samples_per_segment, total_samples)
            
            # 如果最后一段长度不足，使用零填充
            if end - start < samples_per_segment:
                segment = torch.zeros((1, samples_per_segment), dtype=waveform.dtype)
                segment[0, :end-start] = waveform[0, start:end]
            else:
                segment = waveform[:, start:end]
            
            segments.append(segment)
            segment_times.append(start / self.config.SAMPLE_RATE)
            
            # 如果已经处理到结尾，退出循环
            if end == total_samples:
                break
                
        return segments, segment_times
    
    @torch.no_grad()
    def process_segment(self, waveform):
        """
        处理单个音频段
        
        Args:
            waveform: 音频波形
            
        Returns:
            embedding: 声纹嵌入向量
            vad_probs: VAD概率
        """
        # 确保waveform形状正确 [1, samples]
        if waveform.ndim == 1:
            waveform = waveform.unsqueeze(0)
        
        # 移到设备
        waveform = waveform.to(self.device)
        
        # 模型推理
        embedding, vad_logits = self.model(waveform)
        
        # 计算VAD概率
        vad_probs = torch.sigmoid(vad_logits).cpu().numpy()
        
        # 返回CPU上的结果
        return embedding.cpu().numpy(), vad_probs
    
    def compute_similarity(self, embedding1, embedding2):
        """
        计算两个嵌入向量之间的余弦相似度
        
        Args:
            embedding1: 第一个嵌入向量
            embedding2: 第二个嵌入向量
            
        Returns:
            similarity: 余弦相似度
        """
        # 归一化
        embedding1 = embedding1 / np.linalg.norm(embedding1)
        embedding2 = embedding2 / np.linalg.norm(embedding2)
        
        # 计算余弦相似度
        similarity = np.dot(embedding1, embedding2)
        
        return similarity
    
    def identify_speaker(self, embedding, reference_embeddings):
        """
        识别说话人
        
        Args:
            embedding: 测试音频的嵌入向量
            reference_embeddings: 参考嵌入向量字典 {speaker_id: embedding}
            
        Returns:
            identified_speaker: 识别出的说话人ID
            max_similarity: 最高相似度值
            all_similarities: 所有说话人的相似度字典
        """
        similarities = {}
        max_similarity = -1
        identified_speaker = None
        
        for speaker_id, ref_embedding in reference_embeddings.items():
            similarity = self.compute_similarity(embedding, ref_embedding)
            similarities[speaker_id] = float(similarity)
            
            if similarity > max_similarity:
                max_similarity = similarity
                identified_speaker = speaker_id
        
        return identified_speaker, max_similarity, similarities
    
    def run_inference(self, audio_path, reference_embeddings=None, threshold=0.5):
        """
        对单个音频文件进行推理
        
        Args:
            audio_path: 音频文件路径
            reference_embeddings: 参考说话人嵌入向量
            threshold: 说话人识别的阈值
            
        Returns:
            result: 推理结果字典
        """
        # 预处理音频
        waveform, sr = self.preprocess_audio(audio_path)
        if waveform is None:
            return {"error": "Failed to load audio"}
        
        # 分段处理
        segments, segment_times = self.segment_audio(waveform)
        
        # 处理每个段
        embeddings = []
        vad_results = []
        segment_results = []
        
        for i, (segment, start_time) in enumerate(zip(segments, segment_times)):
            # 处理段
            embedding, vad_probs = self.process_segment(segment)
            
            # 计算VAD结果
            has_speech = np.mean(vad_probs) > 0.5
            speech_ratio = np.mean(vad_probs > 0.5)
            
            # 只有含有语音的段才参与说话人识别
            if has_speech and speech_ratio > 0.1:
                embeddings.append(embedding)
            
            # 计算每个段的VAD详细结果
            segment_result = {
                "segment_id": i,
                "start_time": float(start_time),
                "end_time": float(start_time + (segment.shape[1] / self.config.SAMPLE_RATE)),
                "has_speech": bool(has_speech),
                "speech_ratio": float(speech_ratio),
            }
            
            # 如果有参考嵌入向量且该段包含语音，进行说话人识别
            if reference_embeddings and has_speech and speech_ratio > 0.1:
                speaker_id, similarity, all_similarities = self.identify_speaker(embedding, reference_embeddings)
                segment_result["identified_speaker"] = speaker_id
                segment_result["similarity"] = float(similarity)
                segment_result["all_similarities"] = all_similarities
            
            segment_results.append(segment_result)
            vad_results.append({"time": start_time, "vad": float(has_speech), "speech_ratio": float(speech_ratio)})
        
        # 合并所有有效段的嵌入向量
        if embeddings:
            final_embedding = np.mean(embeddings, axis=0)
        else:
            final_embedding = np.zeros(self.config.EMBEDDING_SIZE)
        
        # 构建结果
        result = {
            "file_path": audio_path,
            "has_speech": any(segment["has_speech"] for segment in segment_results),
            "embedding": final_embedding.tolist(),
            "segment_results": segment_results,
            "vad_timeline": vad_results
        }
        
        # 如果有参考嵌入向量，进行整体说话人识别
        if reference_embeddings and embeddings:
            speaker_id, similarity, all_similarities = self.identify_speaker(final_embedding, reference_embeddings)
            result["identified_speaker"] = speaker_id
            result["similarity"] = float(similarity)
            result["all_similarities"] = all_similarities
            result["is_target_speaker"] = similarity > threshold
        
        return result
    
    def visualize_result(self, result, output_path=None):
        """
        可视化结果
        
        Args:
            result: 推理结果
            output_path: 输出路径，如果为None则显示图像
        """
        plt.figure(figsize=(12, 6))
        
        # 绘制VAD时间线
        times = [point["time"] for point in result["vad_timeline"]]
        vad_values = [point["vad"] for point in result["vad_timeline"]]
        speech_ratios = [point["speech_ratio"] for point in result["vad_timeline"]]
        
        plt.subplot(2, 1, 1)
        plt.plot(times, vad_values, 'b-', label='Speech Detected')
        plt.plot(times, speech_ratios, 'r--', label='Speech Ratio')
        plt.xlabel('Time (s)')
        plt.ylabel('VAD')
        plt.legend()
        plt.title('Voice Activity Detection')
        plt.grid(True)
        
        # 绘制说话人识别结果
        if "all_similarities" in result:
            plt.subplot(2, 1, 2)
            speakers = list(result["all_similarities"].keys())
            similarities = list(result["all_similarities"].values())
            
            bars = plt.bar(speakers, similarities)
            plt.xlabel('Speaker')
            plt.ylabel('Similarity')
            plt.title('Speaker Recognition Results')
            plt.ylim([0, 1])
            
            # 标记识别出的说话人
            if "identified_speaker" in result:
                for i, speaker in enumerate(speakers):
                    if speaker == result["identified_speaker"]:
                        bars[i].set_color('red')
        
        plt.tight_layout()
        
        if output_path:
            plt.savefig(output_path)
            plt.close()
        else:
            plt.show()
    
    def generate_reference_embeddings(self, reference_dir):
        """
        为参考说话人生成嵌入向量
        
        Args:
            reference_dir: 参考音频目录，每个子目录对应一个说话人
            
        Returns:
            reference_embeddings: 参考嵌入向量字典
        """
        reference_embeddings = {}
        
        # 遍历参考目录
        for speaker in os.listdir(reference_dir):
            speaker_dir = os.path.join(reference_dir, speaker)
            
            if os.path.isdir(speaker_dir):
                embeddings = []
                
                # 处理说话人目录下的所有音频文件
                for file in os.listdir(speaker_dir):
                    if file.endswith(('.wav', '.mp3')):
                        audio_path = os.path.join(speaker_dir, file)
                        
                        # 预处理音频
                        waveform, _ = self.preprocess_audio(audio_path)
                        if waveform is not None:
                            # 处理音频
                            embedding, vad_probs = self.process_segment(waveform)
                            
                            # 检查是否包含足够的语音
                            if np.mean(vad_probs > 0.5) > 0.1:
                                embeddings.append(embedding)
                
                # 如果收集到足够的嵌入向量，计算平均值
                if embeddings:
                    reference_embeddings[speaker] = np.mean(embeddings, axis=0)
        
        return reference_embeddings


def process_directory(inference, directory, reference_embeddings=None, output_dir=None):
    """
    处理目录下的所有音频文件
    
    Args:
        inference: Inference对象
        directory: 要处理的目录
        reference_embeddings: 参考嵌入向量
        output_dir: 输出目录
    """
    if output_dir:
        os.makedirs(output_dir, exist_ok=True)
    
    results = {}
    
    # 遍历目录
    for root, _, files in os.walk(directory):
        for file in files:
            if file.endswith(('.wav', '.mp3')):
                audio_path = os.path.join(root, file)
                print(f"Processing {audio_path}")
                
                # 运行推理
                result = inference.run_inference(audio_path, reference_embeddings)
                
                # 保存结果
                output_name = os.path.splitext(os.path.basename(audio_path))[0]
                results[output_name] = result
                
                if output_dir:
                    # 保存JSON结果
                    json_path = os.path.join(output_dir, f"{output_name}_result.json")
                    with open(json_path, 'w') as f:
                        json.dump(result, f, indent=2)
                    
                    # 保存可视化结果
                    viz_path = os.path.join(output_dir, f"{output_name}_analysis.png")
                    inference.visualize_result(result, viz_path)
    
    return results


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="VAD+SR推理脚本")
    parser.add_argument("--model", type=str, default="checkpoints/best_model.pth",
                      help="模型文件路径")
    parser.add_argument("--input", type=str, required=True,
                      help="输入音频文件或目录")
    parser.add_argument("--output", type=str, default="results",
                      help="输出目录")
    parser.add_argument("--reference", type=str,
                      help="参考说话人音频目录")
    parser.add_argument("--threshold", type=float, default=0.5,
                      help="说话人识别阈值")
    parser.add_argument("--visualize", action="store_true",
                      help="显示可视化结果")
    
    args = parser.parse_args()
    
    # 初始化推理模型
    inference = Inference(args.model)
    
    # 生成参考嵌入向量
    reference_embeddings = None
    if args.reference:
        print(f"Generating reference embeddings from {args.reference}")
        reference_embeddings = inference.generate_reference_embeddings(args.reference)
        print(f"Generated {len(reference_embeddings)} reference embeddings")
    
    # 创建输出目录
    if args.output:
        os.makedirs(args.output, exist_ok=True)
    
    # 处理输入
    if os.path.isdir(args.input):
        print(f"Processing directory: {args.input}")
        results = process_directory(inference, args.input, reference_embeddings, args.output)
    else:
        print(f"Processing file: {args.input}")
        result = inference.run_inference(args.input, reference_embeddings, args.threshold)
        
        # 保存结果
        if args.output:
            output_name = os.path.splitext(os.path.basename(args.input))[0]
            
            # 保存JSON结果
            json_path = os.path.join(args.output, f"{output_name}_result.json")
            with open(json_path, 'w') as f:
                json.dump(result, f, indent=2)
            
            # 可视化
            if args.visualize:
                viz_path = os.path.join(args.output, f"{output_name}_analysis.png")
                inference.visualize_result(result, viz_path)
            else:
                inference.visualize_result(result, os.path.join(args.output, f"{output_name}_analysis.png"))


if __name__ == "__main__":
    main()
