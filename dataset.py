# -*- coding: utf-8 -*-
"""
数据集加载和预处理模块
处理VeriHealthi数据集，支持多设备音频数据和VAD标注生成
"""

import os
import glob
import random
import numpy as np
import torch
import torchaudio
from torch.utils.data import Dataset, DataLoader
import librosa
from config import Config


class VeriHealthiDataset(Dataset):
    """VeriHealthi数据集类"""
    
    def __init__(self, dataset_root, segment_length=4, sample_rate=8000, 
                 is_training=True, augment=True):
        """
        Args:
            dataset_root: 数据集根目录
            segment_length: 音频分段长度(秒)
            sample_rate: 采样率
            is_training: 是否训练模式
            augment: 是否使用数据增强
        """
        self.dataset_root = dataset_root
        self.segment_length = segment_length
        self.sample_rate = sample_rate
        self.is_training = is_training
        self.augment = augment
        self.segment_samples = int(segment_length * sample_rate)
        
        # 加载数据列表
        self.data_list = self._load_data_list()
        
        print(f"Loaded {len(self.data_list)} samples")
        self._print_data_statistics()
    
    def _load_data_list(self):
        """加载所有音频文件路径和标签"""
        data_list = []
        
        # 1. 加载目标说话人数据 (4个设备)
        for device in Config.DEVICE_PRIORITY:
            device_path = os.path.join(self.dataset_root, device)
            if not os.path.exists(device_path):
                continue
                
            for speaker_id in Config.SPEAKER_MAPPING.keys():
                if speaker_id == 'others':
                    continue
                    
                speaker_path = os.path.join(device_path, speaker_id)
                if not os.path.exists(speaker_path):
                    continue
                
                # 优先使用裁剪好的10s音频
                wav_cut_path = os.path.join(speaker_path, 'wav_cut')
                wav_path = os.path.join(speaker_path, 'wav')
                
                if os.path.exists(wav_cut_path):
                    wav_files = glob.glob(os.path.join(wav_cut_path, '*.wav'))
                elif os.path.exists(wav_path):
                    wav_files = glob.glob(os.path.join(wav_path, '*.wav'))
                else:
                    wav_files = []
                
                for wav_file in wav_files:
                    data_list.append({
                        'path': wav_file,
                        'speaker_id': speaker_id,
                        'speaker_label': Config.SPEAKER_MAPPING[speaker_id],
                        'device': device,
                        'is_speech': True
                    })
        
        # 2. 加载其他说话人数据
        others_path = os.path.join(self.dataset_root, 'others', 'wav')
        if os.path.exists(others_path):
            wav_files = glob.glob(os.path.join(others_path, '*.wav'))
            for wav_file in wav_files:
                data_list.append({
                    'path': wav_file,
                    'speaker_id': 'others',
                    'speaker_label': Config.SPEAKER_MAPPING['others'],
                    'device': 'others',
                    'is_speech': True
                })
        
        # 3. 加载噪声数据 (非人声)
        noise_path = os.path.join(self.dataset_root, 'noise')
        if os.path.exists(noise_path):
            for noise_type in ['cat', 'dog', 'environment_sound', 'music', 'nature_sound', 'noise']:
                noise_type_path = os.path.join(noise_path, noise_type)
                if os.path.exists(noise_type_path):
                    wav_files = glob.glob(os.path.join(noise_type_path, '*.wav'))
                    for wav_file in wav_files:
                        data_list.append({
                            'path': wav_file,
                            'speaker_id': 'noise',
                            'speaker_label': -1,  # 噪声不参与说话人分类
                            'device': 'noise',
                            'is_speech': False
                        })
        
        return data_list
    
    def _print_data_statistics(self):
        """打印数据集统计信息"""
        speaker_counts = {}
        device_counts = {}
        
        for item in self.data_list:
            speaker_id = item['speaker_id']
            device = item['device']
            
            speaker_counts[speaker_id] = speaker_counts.get(speaker_id, 0) + 1
            device_counts[device] = device_counts.get(device, 0) + 1
        
        print("Speaker distribution:")
        for speaker, count in speaker_counts.items():
            print(f"  {speaker}: {count}")
        
        print("Device distribution:")
        for device, count in device_counts.items():
            print(f"  {device}: {count}")
    
    def _load_audio(self, path):
        """加载音频文件并预处理"""
        try:
            # 使用torchaudio加载
            waveform, sr = torchaudio.load(path)
            
            # 转换为单声道
            if waveform.shape[0] > 1:
                waveform = torch.mean(waveform, dim=0, keepdim=True)
            
            # 重采样到目标采样率
            if sr != self.sample_rate:
                resampler = torchaudio.transforms.Resample(sr, self.sample_rate)
                waveform = resampler(waveform)
            
            # 转换为numpy
            waveform = waveform.squeeze(0).numpy()
            
        except Exception as e:
            print(f"Error loading {path}: {e}")
            # 返回静音
            waveform = np.zeros(self.segment_samples)
        
        return waveform
    
    def _segment_audio(self, waveform):
        """对音频进行分段处理"""
        if len(waveform) < self.segment_samples:
            # 如果音频太短，进行零填充
            padding = self.segment_samples - len(waveform)
            waveform = np.pad(waveform, (0, padding), mode='constant')
        elif len(waveform) > self.segment_samples:
            # 如果音频太长，随机截取一段
            if self.is_training:
                start = random.randint(0, len(waveform) - self.segment_samples)
            else:
                start = 0  # 验证时从开头截取
            waveform = waveform[start:start + self.segment_samples]
        
        return waveform
    
    def _generate_vad_labels(self, waveform, is_speech):
        """生成VAD标签"""
        # 计算帧数 (考虑模型的下采样率)
        # 根据模型架构，下采样率为 2*2*2 = 8
        downsample_rate = 8
        frame_length = int(self.sample_rate * Config.VAD_FRAME_LENGTH)  # 25ms
        frame_shift = int(self.sample_rate * Config.VAD_FRAME_SHIFT)    # 10ms
        
        num_frames = (len(waveform) - frame_length) // frame_shift + 1
        # 考虑下采样
        num_frames = num_frames // downsample_rate
        
        if is_speech:
            # 对于语音，进行简单的能量检测来生成VAD标签
            vad_labels = []
            for i in range(0, len(waveform) - frame_length + 1, frame_shift):
                frame = waveform[i:i + frame_length]
                energy = np.sum(frame ** 2)
                # 简单的能量阈值判断
                is_voice = energy > np.percentile(np.sum(
                    np.array([waveform[j:j + frame_length] ** 2 
                             for j in range(0, len(waveform) - frame_length + 1, frame_shift)]), 
                    axis=1), 20)
                vad_labels.append(1.0 if is_voice else 0.0)
            
            # 下采样到匹配模型输出
            if len(vad_labels) > num_frames:
                indices = np.linspace(0, len(vad_labels) - 1, num_frames).astype(int)
                vad_labels = np.array(vad_labels)[indices]
            else:
                vad_labels = np.array(vad_labels)
        else:
            # 对于噪声，全部标记为非语音
            vad_labels = np.zeros(num_frames)
        
        # 确保标签长度正确
        if len(vad_labels) < num_frames:
            vad_labels = np.pad(vad_labels, (0, num_frames - len(vad_labels)))
        elif len(vad_labels) > num_frames:
            vad_labels = vad_labels[:num_frames]
            
        return vad_labels.astype(np.float32)
    
    def _apply_augmentation(self, waveform):
        """应用数据增强"""
        if not self.augment or not self.is_training:
            return waveform
        
        # 随机添加高斯噪声
        if random.random() < 0.3:
            noise_factor = random.uniform(0.001, 0.01)
            noise = np.random.normal(0, noise_factor, waveform.shape)
            waveform = waveform + noise
        
        # 随机音量调整
        if random.random() < 0.3:
            volume_factor = random.uniform(0.7, 1.3)
            waveform = waveform * volume_factor
        
        # 限制幅值范围
        waveform = np.clip(waveform, -1.0, 1.0)
        
        return waveform
    
    def __len__(self):
        return len(self.data_list)
    
    def __getitem__(self, idx):
        item = self.data_list[idx]
        
        # 加载音频
        waveform = self._load_audio(item['path'])
        
        # 分段处理
        waveform = self._segment_audio(waveform)
        
        # 数据增强
        waveform = self._apply_augmentation(waveform)
        
        # 生成VAD标签
        vad_labels = self._generate_vad_labels(waveform, item['is_speech'])
        
        # 转换为张量
        waveform = torch.FloatTensor(waveform)
        vad_labels = torch.FloatTensor(vad_labels)
        
        # 说话人标签 (噪声样本的标签为-1，训练时会被忽略)
        speaker_label = item['speaker_label'] if item['speaker_label'] != -1 else 0
        speaker_label = torch.LongTensor([speaker_label])
        
        # 是否为有效的说话人样本 (用于损失计算)
        is_valid_speaker = item['speaker_label'] != -1
        
        return {
            'waveform': waveform,
            'speaker_label': speaker_label.squeeze(0),
            'vad_labels': vad_labels,
            'is_valid_speaker': is_valid_speaker,
            'path': item['path']
        }


def create_data_loaders(dataset_root, batch_size=32, num_workers=4, 
                       train_ratio=0.8, segment_length=4):
    """创建训练和验证数据加载器"""
    
    # 创建完整数据集
    full_dataset = VeriHealthiDataset(
        dataset_root=dataset_root,
        segment_length=segment_length,
        is_training=True
    )
    
    # 分割训练和验证集
    total_size = len(full_dataset)
    train_size = int(train_ratio * total_size)
    val_size = total_size - train_size
    
    train_dataset, val_dataset = torch.utils.data.random_split(
        full_dataset, [train_size, val_size]
    )
    
    # 为验证集修改设置
    val_dataset.dataset.is_training = False
    val_dataset.dataset.augment = False
    
    # 创建数据加载器
    train_loader = DataLoader(
        train_dataset,
        batch_size=batch_size,
        shuffle=True,
        num_workers=num_workers,
        pin_memory=True,
        drop_last=True
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        pin_memory=True,
        drop_last=False
    )
    
    return train_loader, val_loader


if __name__ == '__main__':
    # 测试数据集
    dataset_root = Config.DATASET_ROOT
    
    if os.path.exists(dataset_root):
        train_loader, val_loader = create_data_loaders(
            dataset_root=dataset_root,
            batch_size=4,
            num_workers=0
        )
        
        print(f"Train batches: {len(train_loader)}")
        print(f"Val batches: {len(val_loader)}")
        
        # 测试一个batch
        for batch in train_loader:
            print(f"Waveform shape: {batch['waveform'].shape}")
            print(f"Speaker label shape: {batch['speaker_label'].shape}")
            print(f"VAD labels shape: {batch['vad_labels'].shape}")
            print(f"Speaker labels: {batch['speaker_label']}")
            print(f"Valid speakers: {batch['is_valid_speaker']}")
            break
    else:
        print(f"Dataset root not found: {dataset_root}")
