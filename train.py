# -*- coding: utf-8 -*-
"""
训练脚本 - VAD + Speaker Recognition 多任务模型训练
"""

import os
import time
import logging
import argparse
from datetime import datetime

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.tensorboard import SummaryWriter

from config import Config
from model import VAD_SR_Model
from dataset import create_data_loaders
from loss import MultiTaskLoss, compute_accuracy, compute_vad_metrics


def setup_logging(log_dir):
    """设置日志记录"""
    os.makedirs(log_dir, exist_ok=True)
    
    log_file = os.path.join(log_dir, f'training_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    
    return logging.getLogger(__name__)


class Trainer:
    """训练器类"""
    
    def __init__(self, model, train_loader, val_loader, config):
        self.model = model
        self.train_loader = train_loader
        self.val_loader = val_loader
        self.config = config
        
        # 设置设备
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.model.to(self.device)
        
        # 损失函数
        self.criterion = MultiTaskLoss(
            num_classes=config.NUM_CLASSES,
            embedding_size=config.EMBEDDING_SIZE,
            vad_weight=config.VAD_LOSS_WEIGHT,
            aam_margin=config.AAM_MARGIN,
            aam_scale=config.AAM_SCALE
        ).to(self.device)
        
        # 优化器 - 使用AdamW获得更好的稳定性
        self.optimizer = optim.AdamW(
            self.model.parameters(),
            lr=config.LEARNING_RATE,
            weight_decay=config.WEIGHT_DECAY,
            betas=(0.9, 0.999),  # 保持默认值
            eps=1e-8
        )
        
        # 学习率调度器 - 使用更温和的调度策略
        self.scheduler = optim.lr_scheduler.StepLR(
            self.optimizer, 
            step_size=getattr(config, 'LR_SCHEDULER_STEP', 20), 
            gamma=getattr(config, 'LR_SCHEDULER_GAMMA', 0.7)
        )
        
        # 添加梯度裁剪参数
        self.grad_clip_norm = 1.0  # 减小梯度裁剪阈值
        
        # TensorBoard
        self.writer = SummaryWriter(log_dir=config.LOG_DIR)
        
        # 训练状态
        self.current_epoch = 0
        self.best_val_loss = float('inf')
        
        # 早停机制
        self.patience = getattr(config, 'EARLY_STOP_PATIENCE', 15)  # 默认15个epoch
        self.early_stop_counter = 0
        self.early_stop = False
        self.min_epochs = getattr(config, 'MIN_EPOCHS', 10)  # 最少训练10个epoch数
        
        # 日志
        self.logger = logging.getLogger(__name__)
        
    def train_epoch(self):
        """训练一个epoch"""
        self.model.train()
        
        total_loss = 0.0
        total_sr_loss = 0.0
        total_vad_loss = 0.0
        total_sr_acc = 0.0
        total_vad_f1 = 0.0
        num_batches = 0
        num_valid_batches = 0  # 有效说话人样本的batch数
        
        start_time = time.time()
        
        for batch_idx, batch in enumerate(self.train_loader):
            # 数据移到设备
            waveforms = batch['waveform'].to(self.device)
            speaker_labels = batch['speaker_label'].to(self.device)
            vad_labels = batch['vad_labels'].to(self.device)
            is_valid_speaker = batch['is_valid_speaker']
            
            # 前向传播
            speaker_embeddings, vad_logits = self.model(waveforms)
            
            # 只对有效的说话人样本计算SR损失
            valid_mask = [speaker for speaker in is_valid_speaker]  # 转换为列表避免tensor警告
            valid_indices = torch.tensor(valid_mask, dtype=torch.bool).to(self.device)
            if valid_indices.sum() > 0:
                valid_embeddings = speaker_embeddings[valid_indices]
                valid_speaker_labels = speaker_labels[valid_indices]
                
                # 计算损失
                total_loss_batch, sr_loss, vad_loss = self.criterion(
                    valid_embeddings, vad_logits, valid_speaker_labels, vad_labels
                )
                
                # 反向传播 - 添加梯度累积和更强的稳定化
                self.optimizer.zero_grad()
                total_loss_batch.backward()
                
                # 更温和的梯度裁剪
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=self.grad_clip_norm)
                
                # 梯度检查和稳定化
                grad_norm = 0.0
                for param in self.model.parameters():
                    if param.grad is not None:
                        grad_norm += param.grad.data.norm(2).item() ** 2
                grad_norm = grad_norm ** 0.5
                
                # 如果梯度过大，跳过这个batch
                if grad_norm > 10.0:
                    self.logger.warning(f"Skipping batch due to large gradient norm: {grad_norm:.2f}")
                    continue
                    
                self.optimizer.step()
                
                # 计算准确率
                sr_logits = self.criterion.sr_loss(valid_embeddings, None)
                sr_acc = compute_accuracy(sr_logits, valid_speaker_labels)
                
                total_sr_acc += sr_acc
                num_valid_batches += 1
            else:
                # 如果没有有效说话人，只计算VAD损失
                vad_loss = self.criterion.vad_loss(vad_logits, vad_labels.float())
                sr_loss = torch.tensor(0.0)
                total_loss_batch = vad_loss
                
                self.optimizer.zero_grad()
                total_loss_batch.backward()
                self.optimizer.step()
                
                sr_acc = 0.0
            
            # 计算VAD指标
            precision, recall, vad_f1 = compute_vad_metrics(vad_logits, vad_labels)
            
            # 累计统计
            total_loss += total_loss_batch.item()
            total_sr_loss += sr_loss.item() if torch.is_tensor(sr_loss) else sr_loss
            total_vad_loss += vad_loss.item()
            total_vad_f1 += vad_f1
            num_batches += 1
            
            # 打印进度
            if batch_idx % 10 == 0:
                self.logger.info(
                    f'Epoch {self.current_epoch}, Batch {batch_idx}/{len(self.train_loader)}, '
                    f'Loss: {total_loss_batch.item():.4f}, '
                    f'SR Loss: {sr_loss.item() if torch.is_tensor(sr_loss) else sr_loss:.4f}, '
                    f'VAD Loss: {vad_loss.item():.4f}, '
                    f'SR Acc: {sr_acc:.4f}, VAD F1: {vad_f1:.4f}'
                )
        
        # 计算平均值
        avg_loss = total_loss / num_batches
        avg_sr_loss = total_sr_loss / num_batches
        avg_vad_loss = total_vad_loss / num_batches
        avg_sr_acc = total_sr_acc / max(num_valid_batches, 1)
        avg_vad_f1 = total_vad_f1 / num_batches
        
        epoch_time = time.time() - start_time
        
        self.logger.info(
            f'Epoch {self.current_epoch} Training - '
            f'Loss: {avg_loss:.4f}, SR Loss: {avg_sr_loss:.4f}, VAD Loss: {avg_vad_loss:.4f}, '
            f'SR Acc: {avg_sr_acc:.4f}, VAD F1: {avg_vad_f1:.4f}, Time: {epoch_time:.2f}s'
        )
        
        # 写入TensorBoard
        self.writer.add_scalar('Train/Total_Loss', avg_loss, self.current_epoch)
        self.writer.add_scalar('Train/SR_Loss', avg_sr_loss, self.current_epoch)
        self.writer.add_scalar('Train/VAD_Loss', avg_vad_loss, self.current_epoch)
        self.writer.add_scalar('Train/SR_Accuracy', avg_sr_acc, self.current_epoch)
        self.writer.add_scalar('Train/VAD_F1', avg_vad_f1, self.current_epoch)
        
        return avg_loss
    
    def validate(self):
        """验证"""
        self.model.eval()
        
        total_loss = 0.0
        total_sr_loss = 0.0
        total_vad_loss = 0.0
        total_sr_acc = 0.0
        total_vad_f1 = 0.0
        num_batches = 0
        num_valid_batches = 0
        
        with torch.no_grad():
            for batch in self.val_loader:
                waveforms = batch['waveform'].to(self.device)
                speaker_labels = batch['speaker_label'].to(self.device)
                vad_labels = batch['vad_labels'].to(self.device)
                is_valid_speaker = batch['is_valid_speaker']
                
                # 前向传播
                speaker_embeddings, vad_logits = self.model(waveforms)
                
                # 只对有效说话人样本计算SR损失
                valid_mask = [speaker for speaker in is_valid_speaker]  # 转换为列表避免tensor警告  
                valid_indices = torch.tensor(valid_mask, dtype=torch.bool).to(self.device)
                if valid_indices.sum() > 0:
                    valid_embeddings = speaker_embeddings[valid_indices]
                    valid_speaker_labels = speaker_labels[valid_indices]
                    
                    total_loss_batch, sr_loss, vad_loss = self.criterion(
                        valid_embeddings, vad_logits, valid_speaker_labels, vad_labels
                    )
                    
                    # 计算准确率
                    sr_logits = self.criterion.sr_loss(valid_embeddings, None)
                    sr_acc = compute_accuracy(sr_logits, valid_speaker_labels)
                    
                    total_sr_acc += sr_acc
                    num_valid_batches += 1
                else:
                    vad_loss = self.criterion.vad_loss(vad_logits, vad_labels.float())
                    sr_loss = torch.tensor(0.0)
                    total_loss_batch = vad_loss
                    sr_acc = 0.0
                
                # 计算VAD指标
                precision, recall, vad_f1 = compute_vad_metrics(vad_logits, vad_labels)
                
                # 累计统计
                total_loss += total_loss_batch.item()
                total_sr_loss += sr_loss.item() if torch.is_tensor(sr_loss) else sr_loss
                total_vad_loss += vad_loss.item()
                total_vad_f1 += vad_f1
                num_batches += 1
        
        # 计算平均值
        avg_loss = total_loss / num_batches
        avg_sr_loss = total_sr_loss / num_batches
        avg_vad_loss = total_vad_loss / num_batches
        avg_sr_acc = total_sr_acc / max(num_valid_batches, 1)
        avg_vad_f1 = total_vad_f1 / num_batches
        
        self.logger.info(
            f'Epoch {self.current_epoch} Validation - '
            f'Loss: {avg_loss:.4f}, SR Loss: {avg_sr_loss:.4f}, VAD Loss: {avg_vad_loss:.4f}, '
            f'SR Acc: {avg_sr_acc:.4f}, VAD F1: {avg_vad_f1:.4f}'
        )
        
        # 写入TensorBoard
        self.writer.add_scalar('Val/Total_Loss', avg_loss, self.current_epoch)
        self.writer.add_scalar('Val/SR_Loss', avg_sr_loss, self.current_epoch)
        self.writer.add_scalar('Val/VAD_Loss', avg_vad_loss, self.current_epoch)
        self.writer.add_scalar('Val/SR_Accuracy', avg_sr_acc, self.current_epoch)
        self.writer.add_scalar('Val/VAD_F1', avg_vad_f1, self.current_epoch)
        
        return avg_loss
    
    def save_checkpoint(self, is_best=False):
        """保存模型检查点"""
        checkpoint = {
            'epoch': self.current_epoch,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict(),
            'best_val_loss': self.best_val_loss,
        }
        
        # 保存最新检查点
        checkpoint_path = os.path.join(self.config.MODEL_SAVE_DIR, 'latest_checkpoint.pth')
        torch.save(checkpoint, checkpoint_path)
        
        # 如果是最佳模型，额外保存
        if is_best:
            best_path = os.path.join(self.config.MODEL_SAVE_DIR, 'best_model.pth')
            torch.save(checkpoint, best_path)
            self.logger.info(f'Saved best model to {best_path}')
    
    def train(self):
        """完整训练流程"""
        self.logger.info(f'Starting training on device: {self.device}')
        self.logger.info(f'Model parameters: {sum(p.numel() for p in self.model.parameters()) / 1e6:.2f}M')
        self.logger.info(f'Early stopping patience: {self.patience} epochs')
        
        for epoch in range(self.config.NUM_EPOCHS):
            self.current_epoch = epoch
            
            # 检查早停条件
            if self.early_stop and epoch >= self.min_epochs:
                self.logger.info(f'Early stopping at epoch {epoch}')
                self.logger.info(f'No improvement for {self.patience} consecutive epochs')
                break
            
            # 训练
            train_loss = self.train_epoch()
            
            # 验证
            val_loss = self.validate()
            
            # 学习率调度
            self.scheduler.step()
            current_lr = self.scheduler.get_last_lr()[0]
            self.writer.add_scalar('Learning_Rate', current_lr, epoch)
            
            # 早停逻辑
            self.check_early_stopping(val_loss)
            
            # 保存检查点
            is_best = val_loss < self.best_val_loss
            if is_best:
                self.best_val_loss = val_loss
            
            self.save_checkpoint(is_best)
            
            self.logger.info(
                f'Epoch {epoch} completed. Best val loss: {self.best_val_loss:.4f}, '
                f'Early stop counter: {self.early_stop_counter}/{self.patience}'
            )
        
        self.writer.close()
        if self.early_stop:
            self.logger.info(f'Training stopped early at epoch {self.current_epoch}!')
        else:
            self.logger.info('Training completed!')
    
    def check_early_stopping(self, val_loss):
        """检查早停条件"""
        if val_loss < self.best_val_loss:
            # 验证损失改善，重置计数器
            self.early_stop_counter = 0
            self.logger.info(f'Validation loss improved: {self.best_val_loss:.4f} -> {val_loss:.4f}')
        else:
            # 验证损失没有改善，增加计数器
            self.early_stop_counter += 1
            self.logger.info(f'No improvement. Counter: {self.early_stop_counter}/{self.patience}')
            
            # 检查是否达到早停条件
            if self.early_stop_counter >= self.patience and self.current_epoch >= self.min_epochs:
                self.early_stop = True
                self.logger.warning(f'Early stopping triggered after {self.patience} epochs without improvement')


def main():
    parser = argparse.ArgumentParser(description='VAD + Speaker Recognition Training')
    parser.add_argument('--dataset_root', type=str, default=Config.DATASET_ROOT,
                       help='Dataset root path')
    parser.add_argument('--batch_size', type=int, default=Config.BATCH_SIZE,
                       help='Batch size')
    parser.add_argument('--num_epochs', type=int, default=Config.NUM_EPOCHS,
                       help='Number of epochs')
    parser.add_argument('--learning_rate', type=float, default=Config.LEARNING_RATE,
                       help='Learning rate')
    parser.add_argument('--resume', type=str, default=None,
                       help='Resume from checkpoint')
    
    # 早停相关参数
    parser.add_argument('--patience', type=int, default=Config.EARLY_STOP_PATIENCE,
                       help='Early stopping patience (epochs)')
    parser.add_argument('--min_epochs', type=int, default=Config.MIN_EPOCHS,
                       help='Minimum epochs before early stopping')
    parser.add_argument('--disable_early_stop', action='store_true',
                       help='Disable early stopping')
    
    args = parser.parse_args()
    
    # 创建配置
    config = Config()
    config.DATASET_ROOT = args.dataset_root
    config.BATCH_SIZE = args.batch_size
    config.NUM_EPOCHS = args.num_epochs
    config.LEARNING_RATE = args.learning_rate
    
    # 早停参数设置
    if args.disable_early_stop:
        config.EARLY_STOP_PATIENCE = float('inf')  # 禁用早停
    else:
        config.EARLY_STOP_PATIENCE = args.patience
    config.MIN_EPOCHS = args.min_epochs
    
    # 创建目录
    config.create_dirs()
    
    # 设置日志
    logger = setup_logging(config.LOG_DIR)
    
    # 检查数据集
    if not os.path.exists(config.DATASET_ROOT):
        logger.error(f"Dataset root not found: {config.DATASET_ROOT}")
        return
    
    # 创建数据加载器
    logger.info("Loading dataset...")
    train_loader, val_loader = create_data_loaders(
        dataset_root=config.DATASET_ROOT,
        batch_size=config.BATCH_SIZE,
        num_workers=config.NUM_WORKERS,
        segment_length=config.SEGMENT_LENGTH
    )
    
    # 创建模型
    model = VAD_SR_Model(
        channels=config.CHANNELS,
        embedding_size=config.EMBEDDING_SIZE,
        sample_rate=config.SAMPLE_RATE
    )
    
    # 创建训练器
    trainer = Trainer(model, train_loader, val_loader, config)
    
    # 恢复检查点
    if args.resume:
        if os.path.exists(args.resume):
            logger.info(f"Resuming from {args.resume}")
            checkpoint = torch.load(args.resume, map_location=trainer.device)
            trainer.model.load_state_dict(checkpoint['model_state_dict'])
            trainer.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
            trainer.scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
            trainer.current_epoch = checkpoint['epoch']
            trainer.best_val_loss = checkpoint['best_val_loss']
        else:
            logger.warning(f"Checkpoint not found: {args.resume}")
    
    # 开始训练
    trainer.train()


if __name__ == '__main__':
    main()
