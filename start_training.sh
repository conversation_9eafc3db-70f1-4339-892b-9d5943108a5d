#!/bin/bash
# 训练启动脚本

echo "=== VAD + Speaker Recognition Training Pipeline ==="
echo "Starting training with the following configuration:"

# 默认参数
DATASET_ROOT="VeriHealthi_SR_Dataset_v2.0"
BATCH_SIZE=32
NUM_EPOCHS=100
LEARNING_RATE=0.001

# 检查数据集
if [ ! -d "$DATASET_ROOT" ]; then
    echo "Error: Dataset not found at $DATASET_ROOT"
    echo "Please ensure the VeriHealthi dataset is properly extracted."
    exit 1
fi

echo "Dataset root: $DATASET_ROOT"
echo "Batch size: $BATCH_SIZE"
echo "Number of epochs: $NUM_EPOCHS"
echo "Learning rate: $LEARNING_RATE"
echo ""

# 检查Python环境和依赖
echo "Checking Python environment..."
python -c "import torch; print(f'PyTorch version: {torch.__version__}')"
python -c "import torchaudio; print(f'Torchaudio version: {torchaudio.__version__}')"

# 检查GPU
python -c "import torch; print(f'CUDA available: {torch.cuda.is_available()}')"
if python -c "import torch; print(torch.cuda.is_available())" | grep -q "True"; then
    python -c "import torch; print(f'GPU count: {torch.cuda.device_count()}')"
    python -c "import torch; print(f'Current GPU: {torch.cuda.get_device_name(0)}')"
fi

echo ""
echo "Starting training..."

# 开始训练
python train.py \
    --dataset_root "$DATASET_ROOT" \
    --batch_size $BATCH_SIZE \
    --num_epochs $NUM_EPOCHS \
    --learning_rate $LEARNING_RATE

echo ""
echo "Training completed!"
echo "Check the 'checkpoints' directory for saved models."
echo "Check the 'logs' directory for training logs."
echo "Use TensorBoard to visualize training progress: tensorboard --logdir logs"
