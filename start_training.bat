@echo off
REM Windows训练启动脚本

echo === VAD + Speaker Recognition Training Pipeline ===
echo Starting training with the following configuration:

REM 默认参数
set DATASET_ROOT=VeriHealthi_SR_Dataset_v2.0
set BATCH_SIZE=32
set NUM_EPOCHS=100
set LEARNING_RATE=0.001

REM 早停参数
set PATIENCE=15
set MIN_EPOCHS=10

REM 检查数据集
if not exist "%DATASET_ROOT%" (
    echo Error: Dataset not found at %DATASET_ROOT%
    echo Please ensure the VeriHealthi dataset is properly extracted.
    pause
    exit /b 1
)

echo Dataset root: %DATASET_ROOT%
echo Batch size: %BATCH_SIZE%
echo Number of epochs: %NUM_EPOCHS%
echo Learning rate: %LEARNING_RATE%
echo Early stopping patience: %PATIENCE%
echo Minimum epochs: %MIN_EPOCHS%
echo.

REM 检查Python环境和依赖
echo Checking Python environment...
@REM python -c "import torch; print(f'PyTorch version: {torch.__version__}')"
@REM python -c "import torchaudio; print(f'Torchaudio version: {torchaudio.__version__}')"

REM 检查GPU
@REM python -c "import torch; print(f'CUDA available: {torch.cuda.is_available()}')" | find "True" >nul
@REM if %errorlevel% == 0 (
@REM     python -c "import torch; print(f'GPU count: {torch.cuda.device_count()}')"
@REM     python -c "import torch; print(f'Current GPU: {torch.cuda.get_device_name(0)}')"
@REM )

echo.
echo Starting training...

REM 开始训练
python train.py --dataset_root "%DATASET_ROOT%" --batch_size %BATCH_SIZE% --num_epochs %NUM_EPOCHS% --learning_rate %LEARNING_RATE% --patience %PATIENCE% --min_epochs %MIN_EPOCHS%

echo.
echo Training completed!
echo Check the 'checkpoints' directory for saved models.
echo Check the 'logs' directory for training logs.
echo Use TensorBoard to visualize training progress: tensorboard --logdir logs

pause
