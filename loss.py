# -*- coding: utf-8 -*-
"""
损失函数定义文件 - 包含AAMSoftmax和多任务损失
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import math


class AAMSoftmax(nn.Module):
    """
    Additive Angular Margin Softmax (ArcFace) for Speaker Recognition
    """
    
    def __init__(self, input_dim, num_classes, margin=0.2, scale=30.0):
        super().__init__()
        self.input_dim = input_dim
        self.num_classes = num_classes
        self.margin = margin
        self.scale = scale
        
        # 权重矩阵 - 每个类别的原型向量
        self.weight = nn.Parameter(torch.FloatTensor(num_classes, input_dim))
        nn.init.xavier_uniform_(self.weight)
        
        self.cos_m = math.cos(margin)
        self.sin_m = math.sin(margin)
        self.threshold = math.cos(math.pi - margin)
        self.mm = math.sin(math.pi - margin) * margin

    def forward(self, embeddings, labels):
        """
        Args:
            embeddings: [batch_size, input_dim] - L2归一化的嵌入向量
            labels: [batch_size] - 类别标签
            
        Returns:
            logits: [batch_size, num_classes]
        """
        # L2归一化嵌入向量和权重
        embeddings = F.normalize(embeddings, p=2, dim=1)
        weight = F.normalize(self.weight, p=2, dim=1)
        
        # 计算余弦相似度
        cosine = F.linear(embeddings, weight)  # [batch_size, num_classes]
        sine = torch.sqrt(1.0 - torch.pow(cosine, 2))
        
        # 添加角度边距
        phi = cosine * self.cos_m - sine * self.sin_m
        
        # 只对正确类别添加边距
        if labels is not None:
            phi = torch.where(cosine > self.threshold, phi, cosine - self.mm)
            
            # 创建one-hot标签
            one_hot = torch.zeros(cosine.size(), device=embeddings.device)
            one_hot.scatter_(1, labels.view(-1, 1).long(), 1)
            
            # 应用边距
            logits = (one_hot * phi) + ((1.0 - one_hot) * cosine)
            logits *= self.scale
        else:
            logits = cosine * self.scale
            
        return logits


class MultiTaskLoss(nn.Module):
    """
    多任务损失函数 - 结合Speaker Recognition和VAD损失
    添加了损失平衡和稳定化机制
    """
    
    def __init__(self, num_classes, embedding_size, vad_weight=0.3, 
                 aam_margin=0.2, aam_scale=30.0):
        super().__init__()
        
        self.vad_weight = vad_weight
        
        # Speaker Recognition损失 - AAMSoftmax
        self.sr_loss = AAMSoftmax(
            input_dim=embedding_size,
            num_classes=num_classes,
            margin=aam_margin,
            scale=aam_scale
        )
        
        # VAD损失 - Binary Cross Entropy with class weights
        # 添加类别权重来平衡正负样本
        self.vad_loss = nn.BCEWithLogitsLoss(reduction='mean')
        
        # 损失平滑参数
        self.loss_ema = 0.9  # 指数移动平均的衰减因子
        self.sr_loss_scale = None
        self.vad_loss_scale = None
        
    def forward(self, speaker_embeddings, vad_logits, speaker_labels, vad_labels):
        """
        Args:
            speaker_embeddings: [batch_size, embedding_size]
            vad_logits: [batch_size, T_frames]  
            speaker_labels: [batch_size]
            vad_labels: [batch_size, T_frames]
            
        Returns:
            total_loss: 总损失
            sr_loss: Speaker Recognition损失
            vad_loss: VAD损失
        """
        # Speaker Recognition损失
        sr_logits = self.sr_loss(speaker_embeddings, speaker_labels)
        sr_loss = F.cross_entropy(sr_logits, speaker_labels)
        
        # VAD损失 - 需要确保维度匹配
        if vad_logits.shape != vad_labels.shape:
            # 如果帧数不匹配，进行插值调整
            if vad_logits.size(-1) != vad_labels.size(-1):
                vad_labels = F.interpolate(
                    vad_labels.unsqueeze(1).float(),
                    size=vad_logits.size(-1),
                    mode='nearest'
                ).squeeze(1)
        
        vad_loss_value = self.vad_loss(vad_logits, vad_labels.float())
        
        # 动态损失平衡 - 使用损失比例来自适应调整权重
        with torch.no_grad():
            if self.sr_loss_scale is None:
                self.sr_loss_scale = sr_loss.detach()
                self.vad_loss_scale = vad_loss_value.detach()
            else:
                self.sr_loss_scale = self.loss_ema * self.sr_loss_scale + (1 - self.loss_ema) * sr_loss.detach()
                self.vad_loss_scale = self.loss_ema * self.vad_loss_scale + (1 - self.loss_ema) * vad_loss_value.detach()
        
        # 归一化损失以减少量级差异
        if self.sr_loss_scale > 0 and self.vad_loss_scale > 0:
            normalized_sr_loss = sr_loss / self.sr_loss_scale
            normalized_vad_loss = vad_loss_value / self.vad_loss_scale
            total_loss = normalized_sr_loss + self.vad_weight * normalized_vad_loss
        else:
            # 如果还没有建立尺度，使用原始权重
            total_loss = sr_loss + self.vad_weight * vad_loss_value
        
        return total_loss, sr_loss, vad_loss_value


def compute_accuracy(logits, labels):
    """计算分类准确率"""
    pred = torch.argmax(logits, dim=1)
    correct = (pred == labels).sum().item()
    total = labels.size(0)
    return correct / total


def compute_vad_metrics(vad_logits, vad_labels, threshold=0.5):
    """
    计算VAD任务的评估指标
    
    Args:
        vad_logits: [batch_size, T_frames] - 模型输出的logits
        vad_labels: [batch_size, T_frames] - 真实标签
        threshold: 二值化阈值
        
    Returns:
        precision, recall, f1_score
    """
    # 将logits转换为概率
    vad_probs = torch.sigmoid(vad_logits)
    vad_pred = (vad_probs > threshold).float()
    
    # 确保维度匹配 - 如果标签和预测维度不匹配，调整标签
    if vad_pred.shape != vad_labels.shape:
        if vad_labels.size(-1) != vad_pred.size(-1):
            # 使用插值调整标签尺寸以匹配预测
            vad_labels = F.interpolate(
                vad_labels.unsqueeze(1).float(),
                size=vad_pred.size(-1),
                mode='nearest'
            ).squeeze(1)
    
    # 展平计算
    vad_pred_flat = vad_pred.view(-1)
    vad_labels_flat = vad_labels.view(-1)
    
    # 确保类型一致
    vad_labels_flat = vad_labels_flat.float()
    
    # 计算TP, FP, FN
    tp = ((vad_pred_flat == 1) & (vad_labels_flat == 1)).sum().item()
    fp = ((vad_pred_flat == 1) & (vad_labels_flat == 0)).sum().item()
    fn = ((vad_pred_flat == 0) & (vad_labels_flat == 1)).sum().item()
    
    # 计算精确率、召回率、F1分数
    precision = tp / (tp + fp + 1e-8)
    recall = tp / (tp + fn + 1e-8)
    f1 = 2 * precision * recall / (precision + recall + 1e-8)
    
    return precision, recall, f1


if __name__ == '__main__':
    # 测试损失函数
    batch_size = 8
    num_classes = 5
    embedding_size = 192
    T_frames = 250
    
    # 创建虚拟数据
    speaker_embeddings = torch.randn(batch_size, embedding_size)
    vad_logits = torch.randn(batch_size, T_frames)
    speaker_labels = torch.randint(0, num_classes, (batch_size,))
    vad_labels = torch.randint(0, 2, (batch_size, T_frames)).float()
    
    # 测试损失函数
    loss_fn = MultiTaskLoss(num_classes, embedding_size)
    total_loss, sr_loss, vad_loss = loss_fn(
        speaker_embeddings, vad_logits, speaker_labels, vad_labels
    )
    
    print(f"Total loss: {total_loss.item():.4f}")
    print(f"SR loss: {sr_loss.item():.4f}")  
    print(f"VAD loss: {vad_loss.item():.4f}")
    
    # 测试评估指标
    sr_logits = loss_fn.sr_loss(speaker_embeddings, None)
    sr_acc = compute_accuracy(sr_logits, speaker_labels)
    print(f"SR accuracy: {sr_acc:.4f}")
    
    precision, recall, f1 = compute_vad_metrics(vad_logits, vad_labels)
    print(f"VAD - Precision: {precision:.4f}, Recall: {recall:.4f}, F1: {f1:.4f}")
