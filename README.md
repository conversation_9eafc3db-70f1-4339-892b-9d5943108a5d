# VAD + Speaker Recognition 多任务模型训练流水线

这是一个基于PyTorch的VAD (Voice Activity Detection) + Speaker Recognition多任务学习模型训练流水线，专门为VeriHealthi数据集设计。

## 功能特性

- **多任务学习**: 同时训练VAD和说话人识别任务，共享特征提取网络
- **模块化设计**: 代码分为配置、模型、数据集、损失函数和训练脚本等独立模块
- **数据增强**: 支持音频数据增强(噪声添加、音量调整等)
- **多设备支持**: 支持处理不同设备(手机、开发板、PC、平板)采集的音频数据
- **灵活配置**: 通过配置文件轻松调整超参数
- **可视化**: 集成TensorBoard训练过程可视化

## 模型架构

模型基于共享主干网络的多任务学习架构：

```
输入音频 → SincConv前端 → 残差块(下采样) → 共享特征
                                          ├─ VAD头 → VAD概率
                                          └─ SR头 → 说话人嵌入
```

- **前端**: SincConv层进行可学习的音频特征提取
- **主干**: 4个残差块，集成SE注意力机制
- **VAD分支**: 1D卷积进行逐帧语音活动检测
- **SR分支**: 注意力统计池化生成说话人嵌入向量

## 文件结构

```
algo/
├── config.py              # 配置文件 - 超参数和路径设置
├── model.py               # 模型定义 - VAD_SR_Model架构
├── dataset.py             # 数据集处理 - VeriHealthi数据加载
├── loss.py                # 损失函数 - AAMSoftmax和多任务损失
├── train.py               # 训练脚本 - 完整训练流程
├── inference.py           # 推理脚本 - 模型推理和评估
├── requirements.txt       # Python依赖包列表
├── start_training.sh      # Linux训练启动脚本
├── start_training.bat     # Windows训练启动脚本
└── README.md             # 说明文档
```

## 快速开始

### 1. 环境准备

```bash
# 安装依赖包
pip install -r requirements.txt

# 或者手动安装主要依赖
pip install torch torchaudio librosa numpy matplotlib tensorboard
```

### 2. 数据准备

确保VeriHealthi数据集按照以下结构组织：

```
VeriHealthi_SR_Dataset_v2.0/
├── board/          # 开发板采集数据
│   ├── XiaoXin/
│   ├── XiaoYuan/
│   ├── XiaoSi/
│   ├── XiaoLai/
│   └── ID1-ID11/
├── phone/          # 手机采集数据
├── pc/            # 电脑采集数据  
├── ipad/          # 平板采集数据
├── others/        # 其他说话人数据
└── noise/         # 噪声数据
    ├── cat/
    ├── dog/
    ├── music/
    └── ...
```

### 3. 开始训练

**Linux/Mac:**
```bash
chmod +x start_training.sh
./start_training.sh
```

**Windows:**
```batch
start_training.bat
```

**或者直接使用Python:**
```bash
python train.py --dataset_root VeriHealthi_SR_Dataset_v2.0 --batch_size 32 --num_epochs 100
```

### 4. 监控训练

```bash
# 启动TensorBoard查看训练过程
tensorboard --logdir logs
```

在浏览器中打开 http://localhost:6006 查看训练指标。

## 配置说明

主要配置参数在 `config.py` 中：

```python
# 模型参数
SAMPLE_RATE = 8000          # 音频采样率
CHANNELS = 32               # 模型通道数
EMBEDDING_SIZE = 192        # 说话人嵌入维度
NUM_CLASSES = 5             # 分类数(4个目标+1个其他)

# 训练参数  
BATCH_SIZE = 32
NUM_EPOCHS = 100
LEARNING_RATE = 0.001
VAD_LOSS_WEIGHT = 0.5       # VAD损失权重α

# AAMSoftmax参数
AAM_MARGIN = 0.2            # 角度边距
AAM_SCALE = 30.0            # 缩放因子
```

## 模型推理

训练完成后，使用推理脚本进行测试：

```bash
python inference.py \
    --model_path checkpoints/best_model.pth \
    --audio_path test_audio.wav \
    --visualize
```

推理结果包括：
- **说话人嵌入**: 用于说话人识别的特征向量
- **VAD概率**: 逐帧的语音活动概率
- **语音区间**: 检测到的语音活动时间段

## 损失函数

模型使用多任务损失函数：

```
L_total = L_sr + α * L_vad
```

- **L_sr**: AAMSoftmax损失用于说话人识别
- **L_vad**: BCEWithLogitsLoss用于VAD任务  
- **α**: 平衡两个任务的权重(默认0.5)

## 性能优化建议

1. **数据平衡**: 确保各说话人和设备的数据量相对平衡
2. **批次大小**: 根据GPU内存调整batch_size
3. **学习率**: 可尝试学习率调度策略
4. **数据增强**: 根据需要启用/禁用音频增强
5. **模型尺寸**: 调整channels参数控制模型大小

## 常见问题

**Q: 训练过程中GPU内存不足怎么办？**
A: 减小batch_size或减少模型channels参数。

**Q: 如何处理数据不平衡问题？**
A: 数据集加载器会自动统计数据分布，可在训练时应用类别权重。

**Q: VAD效果不理想怎么办？** 
A: 调整VAD_LOSS_WEIGHT参数，或检查VAD标签生成逻辑。

**Q: 如何添加新的说话人类别？**
A: 修改config.py中的SPEAKER_MAPPING和NUM_CLASSES。

## 扩展功能

- 支持实时音频流处理
- 集成更多音频增强技术
- 支持不同的池化策略
- 添加更多评估指标

## 参考文献

- AAMSoftmax: ArcFace: Additive Angular Margin Loss for Deep Face Recognition
- SincNet: Speaker Recognition from Raw Waveform with SincNet  
- ResNet: Deep Residual Learning for Image Recognition

## 许可证

本项目仅供学术研究使用。
