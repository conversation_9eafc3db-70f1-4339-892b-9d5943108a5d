# 自动停止功能使用指南

## 🔄 Early Stopping (早停) 功能

训练脚本现在支持**自动早停**功能，可以在模型性能不再改善时自动停止训练，避免过拟合和浪费计算资源。

### 📋 功能特点

1. **智能停止**: 连续N个epoch验证损失没有改善时自动停止
2. **最小训练**: 保证至少训练M个epoch后才能触发早停
3. **灵活配置**: 可通过配置文件或命令行参数调整
4. **详细日志**: 记录早停过程和触发原因

### ⚙️ 配置参数

#### 在 `config.py` 中设置:
```python
# 早停参数
EARLY_STOP_PATIENCE = 15  # 连续15个epoch没有改善就停止
MIN_EPOCHS = 10          # 最少训练10个epoch
```

#### 命令行参数:
```bash
# 使用默认早停设置
python train.py

# 自定义早停patience
python train.py --patience 20

# 设置最小训练epoch数
python train.py --min_epochs 15

# 完全禁用早停
python train.py --disable_early_stop
```

### 🚀 使用示例

#### 1. 标准训练（推荐）
```bash
python train.py --patience 15 --min_epochs 10
```

#### 2. 快速实验（较激进的早停）
```bash
python train.py --patience 5 --min_epochs 3
```

#### 3. 长时间训练（保守的早停）
```bash
python train.py --patience 30 --min_epochs 20
```

#### 4. 完整训练（禁用早停）
```bash
python train.py --disable_early_stop --num_epochs 100
```

### 📊 日志输出示例

训练过程中会看到类似输出：
```
2024-01-15 10:30:15 - INFO - Starting training on device: cuda:0
2024-01-15 10:30:15 - INFO - Early stopping patience: 15 epochs
...
2024-01-15 10:32:20 - INFO - Validation loss improved: 2.345 -> 2.123
2024-01-15 10:33:25 - INFO - No improvement. Counter: 1/15
2024-01-15 10:34:30 - INFO - No improvement. Counter: 2/15
...
2024-01-15 10:45:10 - WARNING - Early stopping triggered after 15 epochs without improvement
2024-01-15 10:45:10 - INFO - Early stopping at epoch 28
2024-01-15 10:45:10 - INFO - Training stopped early at epoch 28!
```

### 🛠️ 工作原理

1. **监控指标**: 验证损失 (validation loss)
2. **改善判断**: 新的验证损失 < 历史最佳验证损失
3. **计数逻辑**: 
   - 改善时：重置计数器为0
   - 没改善：计数器+1
4. **触发条件**: 
   - 计数器 >= patience
   - 当前epoch >= min_epochs

### 💡 建议设置

| 场景 | Patience | Min Epochs | 说明 |
|------|----------|------------|------|
| 快速调试 | 5-8 | 3-5 | 快速验证模型是否能训练 |
| 正常训练 | 10-20 | 5-10 | 平衡效率和性能 |
| 精细调优 | 25-50 | 15-30 | 给模型更多机会找到最优解 |
| 生产环境 | 15-25 | 10-15 | 兼顾性能和资源效率 |

### 🔍 故障排除

**问题**: 训练过早停止
- **解决**: 增加patience值或减少min_epochs

**问题**: 训练时间太长
- **解决**: 减少patience值，确保学习率设置合理

**问题**: 想要固定训练轮数
- **解决**: 使用 `--disable_early_stop` 禁用早停

### 📈 最佳实践

1. **初期训练**: 使用较小的patience (5-10) 快速筛选超参数
2. **正式训练**: 使用中等patience (15-25) 获得最佳性能
3. **监控验证**: 结合TensorBoard观察验证曲线趋势
4. **保存最佳**: 系统会自动保存验证损失最低的模型
