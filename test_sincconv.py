# -*- coding: utf-8 -*-
"""
测试SincConv维度是否正确
"""

import torch
from model import SincConv_fast

def test_sincconv():
    print("Testing SincConv dimensions...")
    
    # 创建SincConv层
    sincconv = SincConv_fast(
        in_channels=1, 
        out_channels=32, 
        kernel_size=129,  # 原来的kernel_size
        sample_rate=8000
    )
    
    print(f"Actual kernel_size: {sincconv.kernel_size}")
    print(f"n_ shape: {sincconv.n_.shape}")
    print(f"window_ shape: {sincconv.window_.shape}")
    
    # 创建测试输入
    batch_size = 4
    seq_length = 16000  # 2秒音频
    
    x = torch.randn(batch_size, 1, seq_length)
    print(f"Input shape: {x.shape}")
    
    try:
        output = sincconv(x)
        print(f"Output shape: {output.shape}")
        print("✅ SincConv test passed!")
        return True
    except Exception as e:
        print(f"❌ SincConv test failed: {e}")
        return False

if __name__ == '__main__':
    test_sincconv()
