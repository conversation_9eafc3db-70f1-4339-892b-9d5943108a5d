# -*- coding: utf-8 -*-
"""
训练监控脚本 - 分析损失波动和模型稳定性
"""

import torch
import numpy as np
import matplotlib.pyplot as plt
from collections import deque
import logging

class TrainingMonitor:
    """训练过程监控器"""
    
    def __init__(self, window_size=50):
        self.window_size = window_size
        self.losses = deque(maxlen=window_size)
        self.sr_losses = deque(maxlen=window_size)
        self.vad_losses = deque(maxlen=window_size)
        self.sr_accs = deque(maxlen=window_size)
        self.vad_f1s = deque(maxlen=window_size)
        self.grad_norms = deque(maxlen=window_size)
        
        self.logger = logging.getLogger(__name__)
        
    def update(self, loss, sr_loss, vad_loss, sr_acc, vad_f1, grad_norm=None):
        """更新监控指标"""
        self.losses.append(loss)
        self.sr_losses.append(sr_loss)
        self.vad_losses.append(vad_loss)
        self.sr_accs.append(sr_acc)
        self.vad_f1s.append(vad_f1)
        if grad_norm is not None:
            self.grad_norms.append(grad_norm)
    
    def get_stability_metrics(self):
        """计算稳定性指标"""
        if len(self.losses) < 10:
            return None
            
        metrics = {}
        
        # 损失波动性（标准差/均值）
        losses = np.array(self.losses)
        metrics['loss_cv'] = np.std(losses) / np.mean(losses) if np.mean(losses) > 0 else 0
        
        sr_losses = np.array(self.sr_losses)
        metrics['sr_loss_cv'] = np.std(sr_losses) / np.mean(sr_losses) if np.mean(sr_losses) > 0 else 0
        
        vad_losses = np.array(self.vad_losses)
        metrics['vad_loss_cv'] = np.std(vad_losses) / np.mean(vad_losses) if np.mean(vad_losses) > 0 else 0
        
        # 趋势分析（最近10个batch的斜率）
        if len(losses) >= 10:
            recent_losses = losses[-10:]
            x = np.arange(len(recent_losses))
            slope = np.polyfit(x, recent_losses, 1)[0]
            metrics['loss_trend'] = slope
        
        return metrics
    
    def check_instability(self):
        """检查训练不稳定的信号"""
        metrics = self.get_stability_metrics()
        if metrics is None:
            return False
            
        warnings = []
        
        # 损失波动过大
        if metrics['loss_cv'] > 0.5:
            warnings.append(f"High loss volatility: CV={metrics['loss_cv']:.3f}")
            
        # SR损失波动过大
        if metrics['sr_loss_cv'] > 0.3:
            warnings.append(f"High SR loss volatility: CV={metrics['sr_loss_cv']:.3f}")
            
        # 损失上升趋势
        if 'loss_trend' in metrics and metrics['loss_trend'] > 0.1:
            warnings.append(f"Loss increasing trend: slope={metrics['loss_trend']:.3f}")
            
        # 梯度爆炸
        if len(self.grad_norms) > 0:
            recent_grad_norm = np.mean(list(self.grad_norms)[-10:])
            if recent_grad_norm > 5.0:
                warnings.append(f"Large gradient norm: {recent_grad_norm:.2f}")
        
        for warning in warnings:
            self.logger.warning(f"Training instability detected: {warning}")
            
        return len(warnings) > 0
    
    def suggest_adjustments(self):
        """基于监控指标建议调整"""
        metrics = self.get_stability_metrics()
        if metrics is None:
            return []
            
        suggestions = []
        
        # 根据损失波动性给出建议
        if metrics['loss_cv'] > 0.5:
            suggestions.append("Consider reducing learning rate (current instability)")
            suggestions.append("Consider using gradient accumulation")
            
        if metrics['sr_loss_cv'] > 0.3:
            suggestions.append("SR loss unstable - check AAM parameters (margin, scale)")
            suggestions.append("Consider reducing batch size for SR stability")
            
        if 'loss_trend' in metrics and metrics['loss_trend'] > 0.1:
            suggestions.append("Loss trending up - reduce learning rate or check data")
            
        return suggestions


def add_monitoring_to_trainer(trainer_class):
    """为训练器添加监控功能的装饰器"""
    
    original_train_epoch = trainer_class.train_epoch
    
    def enhanced_train_epoch(self):
        # 添加监控器
        if not hasattr(self, 'monitor'):
            self.monitor = TrainingMonitor()
            
        # 执行原始训练
        avg_loss = original_train_epoch(self)
        
        # 检查稳定性并给出建议
        if hasattr(self, 'monitor') and len(self.monitor.losses) > 20:
            if self.monitor.check_instability():
                suggestions = self.monitor.suggest_adjustments()
                for suggestion in suggestions:
                    self.logger.info(f"💡 Suggestion: {suggestion}")
        
        return avg_loss
    
    trainer_class.train_epoch = enhanced_train_epoch
    return trainer_class


def plot_training_curves(log_file):
    """从日志文件绘制训练曲线"""
    import re
    
    # 解析日志文件
    losses = []
    sr_losses = []
    vad_losses = []
    sr_accs = []
    vad_f1s = []
    
    pattern = r'Loss: ([\d.]+), SR Loss: ([\d.]+), VAD Loss: ([\d.]+), SR Acc: ([\d.]+), VAD F1: ([\d.]+)'
    
    with open(log_file, 'r', encoding='utf-8') as f:
        for line in f:
            match = re.search(pattern, line)
            if match and 'Batch' in line:  # 只取batch级别的日志
                losses.append(float(match.group(1)))
                sr_losses.append(float(match.group(2)))
                vad_losses.append(float(match.group(3)))
                sr_accs.append(float(match.group(4)))
                vad_f1s.append(float(match.group(5)))
    
    if not losses:
        print("No training data found in log file")
        return
    
    # 绘制图表
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle('Training Monitoring', fontsize=16)
    
    # 损失曲线
    axes[0, 0].plot(losses, label='Total Loss', alpha=0.7)
    axes[0, 0].plot(sr_losses, label='SR Loss', alpha=0.7)
    axes[0, 0].plot(vad_losses, label='VAD Loss', alpha=0.7)
    axes[0, 0].set_title('Loss Curves')
    axes[0, 0].set_xlabel('Batch')
    axes[0, 0].set_ylabel('Loss')
    axes[0, 0].legend()
    axes[0, 0].grid(True)
    
    # 准确率曲线  
    axes[0, 1].plot(sr_accs, label='SR Accuracy', color='orange', alpha=0.7)
    axes[0, 1].plot(vad_f1s, label='VAD F1', color='green', alpha=0.7)
    axes[0, 1].set_title('Accuracy Metrics')
    axes[0, 1].set_xlabel('Batch')
    axes[0, 1].set_ylabel('Score')
    axes[0, 1].legend()
    axes[0, 1].grid(True)
    
    # 损失波动分析
    if len(losses) > 50:
        window = 20
        loss_std = [np.std(losses[i:i+window]) for i in range(len(losses)-window+1)]
        axes[1, 0].plot(loss_std, label='Loss Std (20-batch window)')
        axes[1, 0].set_title('Loss Volatility')
        axes[1, 0].set_xlabel('Batch')
        axes[1, 0].set_ylabel('Standard Deviation')
        axes[1, 0].legend()
        axes[1, 0].grid(True)
    
    # SR vs VAD 损失关系
    axes[1, 1].scatter(sr_losses, vad_losses, alpha=0.6)
    axes[1, 1].set_title('SR Loss vs VAD Loss')
    axes[1, 1].set_xlabel('SR Loss')
    axes[1, 1].set_ylabel('VAD Loss')
    axes[1, 1].grid(True)
    
    plt.tight_layout()
    plt.savefig('training_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # 计算统计信息
    print("\n=== Training Statistics ===")
    print(f"Total batches: {len(losses)}")
    print(f"Loss - Mean: {np.mean(losses):.4f}, Std: {np.std(losses):.4f}, CV: {np.std(losses)/np.mean(losses):.3f}")
    print(f"SR Loss - Mean: {np.mean(sr_losses):.4f}, Std: {np.std(sr_losses):.4f}, CV: {np.std(sr_losses)/np.mean(sr_losses):.3f}")
    print(f"VAD Loss - Mean: {np.mean(vad_losses):.4f}, Std: {np.std(vad_losses):.4f}, CV: {np.std(vad_losses)/np.mean(vad_losses):.3f}")
    print(f"SR Acc - Mean: {np.mean(sr_accs):.4f}, Std: {np.std(sr_accs):.4f}")
    print(f"VAD F1 - Mean: {np.mean(vad_f1s):.4f}, Std: {np.std(vad_f1s):.4f}")


if __name__ == '__main__':
    import sys
    if len(sys.argv) > 1:
        plot_training_curves(sys.argv[1])
    else:
        print("Usage: python monitor_training.py <log_file>")
        # 示例：尝试找到最新的日志文件
        import glob
        log_files = glob.glob('logs/training_*.log')
        if log_files:
            latest_log = max(log_files, key=lambda x: x.split('_')[-1])
            print(f"Analyzing latest log: {latest_log}")
            plot_training_curves(latest_log)
